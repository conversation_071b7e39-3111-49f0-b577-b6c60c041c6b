import{j as s,$ as e}from"./app-CSPm8fiq.js";import{U as i,C as l,S as n}from"./app-layout-CtjvFf2e.js";import{B as m}from"./book-open-qP3kpa8t.js";import{M as d}from"./message-circle-Dp-T4UGq.js";import{a as o}from"./app-logo-icon-RBJwxtah.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],x=o("TrendingUp",h),u=()=>s.jsx("div",{className:"sidebar-content",children:s.jsxs("div",{className:"card shadow-sm",children:[s.jsx("div",{className:"card-header bg-unilink-primary text-white",children:s.jsx("h5",{className:"mb-0",children:"Menu"})}),s.jsxs("div",{className:"list-group list-group-flush",children:[s.jsxs(e,{href:"/profile",className:"list-group-item list-group-item-action",children:[s.jsx(i,{className:"w-4 h-4 me-2"}),"Profile"]}),s.jsxs(e,{href:"/organizations",className:"list-group-item list-group-item-action",children:[s.jsx(i,{className:"w-4 h-4 me-2"}),"My Organizations"]}),s.jsxs(e,{href:"/events",className:"list-group-item list-group-item-action",children:[s.jsx(l,{className:"w-4 h-4 me-2"}),"Events"]}),s.jsxs(e,{href:"/courses",className:"list-group-item list-group-item-action",children:[s.jsx(m,{className:"w-4 h-4 me-2"}),"Courses"]}),s.jsxs(e,{href:"/messages",className:"list-group-item list-group-item-action",children:[s.jsx(d,{className:"w-4 h-4 me-2"}),"Messages"]}),s.jsxs(e,{href:"/settings",className:"list-group-item list-group-item-action",children:[s.jsx(n,{className:"w-4 h-4 me-2"}),"Settings"]})]})]})}),j=()=>s.jsxs("div",{className:"sidebar-content",children:[s.jsxs("div",{className:"card shadow-sm mb-4",children:[s.jsx("div",{className:"card-header bg-unilink-primary text-white",children:s.jsxs("h5",{className:"mb-0 d-flex align-items-center",children:[s.jsx(x,{className:"w-4 h-4 me-2"}),"Trending"]})}),s.jsxs("div",{className:"card-body",children:[s.jsx("div",{className:"small mb-2",children:s.jsx("a",{href:"#",className:"text-decoration-none",children:"#StudentLife"})}),s.jsx("div",{className:"small mb-2",children:s.jsx("a",{href:"#",className:"text-decoration-none",children:"#Academics"})}),s.jsx("div",{className:"small mb-2",children:s.jsx("a",{href:"#",className:"text-decoration-none",children:"#CampusEvents"})}),s.jsx("div",{className:"small",children:s.jsx("a",{href:"#",className:"text-decoration-none",children:"#Research"})})]})]}),s.jsxs("div",{className:"card shadow-sm mb-4",children:[s.jsx("div",{className:"card-header bg-unilink-primary text-white",children:s.jsxs("h5",{className:"mb-0 d-flex align-items-center",children:[s.jsx(l,{className:"w-4 h-4 me-2"}),"Upcoming Events"]})}),s.jsxs("div",{className:"card-body",children:[s.jsx("div",{className:"small text-muted mb-2",children:"Enrollment Period: Aug 1-15"}),s.jsx("div",{className:"small text-muted mb-2",children:"Orientation Day: Aug 20"}),s.jsx("div",{className:"small text-muted mb-2",children:"First Day of Classes: Aug 22"}),s.jsx("div",{className:"small text-muted",children:"Foundation Day: Sept 15"})]})]}),s.jsxs("div",{className:"card shadow-sm",children:[s.jsx("div",{className:"card-header bg-unilink-primary text-white",children:s.jsx("h5",{className:"mb-0",children:"Quick Links"})}),s.jsxs("div",{className:"list-group list-group-flush",children:[s.jsx("a",{href:"https://sksu.edu.ph/",target:"_blank",rel:"noopener noreferrer",className:"list-group-item list-group-item-action",children:"University Website"}),s.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Student Portal"}),s.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Library Resources"}),s.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Academic Calendar"})]})]})]});function b({children:r,leftSidebar:t,rightSidebar:c,showDefaultSidebars:a=!0}){return s.jsx("div",{className:"main-container",children:s.jsx("div",{className:"content-area",children:s.jsx("div",{className:"three-column-container",children:s.jsxs("div",{className:"three-column-wrapper",children:[s.jsx("div",{className:"three-column-left",children:t||a&&s.jsx(u,{})}),s.jsx("div",{className:"three-column-main",children:s.jsx("div",{className:"main-content",children:r})}),s.jsx("div",{className:"three-column-right",children:c||a&&s.jsx(j,{})})]})})})})}export{b as T};
