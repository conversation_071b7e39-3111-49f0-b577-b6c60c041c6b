import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import ThreeColumnLayout from '@/layouts/three-column-layout';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Example Page',
        href: '/example',
    },
];

// Example of custom left sidebar
const CustomLeftSidebar = () => (
    <div className="sidebar-content">
        <div className="card shadow-sm">
            <div className="card-header bg-unilink-primary text-white">
                <h5 className="mb-0">Custom Menu</h5>
            </div>
            <div className="card-body">
                <p>This is a custom left sidebar for this specific page.</p>
            </div>
        </div>
    </div>
);

// Example of custom right sidebar
const CustomRightSidebar = () => (
    <div className="sidebar-content">
        <div className="card shadow-sm">
            <div className="card-header bg-unilink-primary text-white">
                <h5 className="mb-0">Page Info</h5>
            </div>
            <div className="card-body">
                <p>This is a custom right sidebar with page-specific content.</p>
            </div>
        </div>
    </div>
);

export default function ExamplePage() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Example Page" />

            {/* Example 1: Using default sidebars */}
            <ThreeColumnLayout>
                <div className="card shadow-sm">
                    <div className="card-header">
                        <h4 className="mb-0">Example with Default Sidebars</h4>
                    </div>
                    <div className="card-body">
                        <p>This page uses the default left and right sidebars from the ThreeColumnLayout component.</p>
                        <p>The layout automatically provides consistent navigation and quick links.</p>
                    </div>
                </div>
            </ThreeColumnLayout>

            {/* Example 2: Using custom sidebars */}
            {/* 
            <ThreeColumnLayout 
                leftSidebar={<CustomLeftSidebar />}
                rightSidebar={<CustomRightSidebar />}
            >
                <div className="card shadow-sm">
                    <div className="card-header">
                        <h4 className="mb-0">Example with Custom Sidebars</h4>
                    </div>
                    <div className="card-body">
                        <p>This example shows how to provide custom sidebar content.</p>
                    </div>
                </div>
            </ThreeColumnLayout>
            */}

            {/* Example 3: No sidebars */}
            {/* 
            <ThreeColumnLayout showDefaultSidebars={false}>
                <div className="card shadow-sm">
                    <div className="card-header">
                        <h4 className="mb-0">Example without Sidebars</h4>
                    </div>
                    <div className="card-body">
                        <p>This example shows the layout with no sidebars, giving full width to the main content.</p>
                    </div>
                </div>
            </ThreeColumnLayout>
            */}
        </AppLayout>
    );
}
