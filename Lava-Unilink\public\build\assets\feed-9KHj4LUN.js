import{r as t,j as e,K as le,L as ne,$ as I}from"./app-FvBJISK9.js";import{b as ie,c as re,d as ce,B as P,X as T,e as oe,U as H,A as de,C as me,S as he}from"./app-layout-BobmQngD.js";import{P as ue}from"./post-card-BNULB3x8.js";import{a as Y,B as M}from"./app-logo-icon-CT9-NZ3f.js";import{I as Q}from"./input-VMGj1HUy.js";import{L as w}from"./label-BxUs1jY6.js";import{S as R,a as B,b as V,c as q,d as F}from"./select-9wA7k3kZ.js";import{F as J}from"./filter-RgG-B9-k.js";import{D as xe,a as pe,b as je}from"./dialog-reNigUMq.js";import{F as W}from"./file-text-Cjkc8TLg.js";import{T as ge}from"./trash-2-7ZqWWuCq.js";import{G as be}from"./globe-BcOzbEnc.js";import{L as ve}from"./lock-BM0yVQCQ.js";import{P as X}from"./plus-CEEi6mL-.js";import"./index-CJBSpWUr.js";import"./index-DBDRXzgx.js";import"./pin-CAZ9TmU0.js";import"./message-circle-2xcOgaom.js";import"./index-BcX-BwBX.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],Ne=Y("Image",fe);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],we=Y("RotateCcw",ye);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],Ce=Y("Upload",_e),G=[{value:"announcement",label:"Announcements"},{value:"discussion",label:"Discussions"},{value:"event",label:"Events"},{value:"news",label:"News"}],Se=[{value:"created_at",label:"Latest"},{value:"popularity",label:"Most Popular"},{value:"trending",label:"Trending"},{value:"reactions_count",label:"Most Liked"},{value:"comments_count",label:"Most Discussed"}],ke=["Main Campus","Isulan Campus","ACCESS Campus","Tacurong Campus","Bagumbayan Campus"];function ze({filters:u,onFiltersChange:S,onClose:i}){var C,$;const[l,v]=t.useState(u),[f,g]=t.useState([]),[L,A]=t.useState(!1);t.useEffect(()=>{(async()=>{var h;A(!0);try{const N=await fetch("/api/v1/organizations?per_page=100",{headers:{Authorization:`Bearer ${(h=document.querySelector('meta[name="api-token"]'))==null?void 0:h.getAttribute("content")}`,Accept:"application/json"}});if(N.ok){const s=await N.json();g(s.data||[])}}catch(N){console.error("Error loading organizations:",N)}finally{A(!1)}})()},[]);const b=(a,h)=>{const N=h==="all"||h==="personal"?"":h;v(s=>({...s,[a]:N}))},d=()=>{S(l),i()},_=()=>{const a={type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"};v(a),S(a)},y=()=>Object.values(l).filter(a=>a!==""&&a!=="created_at"&&a!=="desc").length,m=a=>{const h={...l};a==="sort_by"?h[a]="created_at":a==="sort_order"?h[a]="desc":h[a]="",v(h)};return e.jsxs(ie,{className:"w-full",children:[e.jsxs(re,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(J,{className:"w-5 h-5"}),e.jsx(ce,{children:"Filter Posts"}),y()>0&&e.jsxs(P,{variant:"secondary",children:[y()," active"]})]}),e.jsx(M,{variant:"ghost",size:"sm",onClick:i,children:e.jsx(T,{className:"w-4 h-4"})})]}),e.jsxs(oe,{className:"space-y-6",children:[y()>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{className:"text-sm font-medium",children:"Active Filters"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[l.type&&e.jsxs(P,{variant:"outline",className:"flex items-center gap-1",children:["Type: ",(C=G.find(a=>a.value===l.type))==null?void 0:C.label,e.jsx("button",{onClick:()=>m("type"),children:e.jsx(T,{className:"w-3 h-3"})})]}),l.organization_id&&e.jsxs(P,{variant:"outline",className:"flex items-center gap-1",children:["Org: ",($=f.find(a=>a.id.toString()===l.organization_id))==null?void 0:$.name,e.jsx("button",{onClick:()=>m("organization_id"),children:e.jsx(T,{className:"w-3 h-3"})})]}),l.campus&&e.jsxs(P,{variant:"outline",className:"flex items-center gap-1",children:["Campus: ",l.campus,e.jsx("button",{onClick:()=>m("campus"),children:e.jsx(T,{className:"w-3 h-3"})})]}),l.date_from&&e.jsxs(P,{variant:"outline",className:"flex items-center gap-1",children:["From: ",l.date_from,e.jsx("button",{onClick:()=>m("date_from"),children:e.jsx(T,{className:"w-3 h-3"})})]}),l.date_to&&e.jsxs(P,{variant:"outline",className:"flex items-center gap-1",children:["To: ",l.date_to,e.jsx("button",{onClick:()=>m("date_to"),children:e.jsx(T,{className:"w-3 h-3"})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"type",children:"Post Type"}),e.jsxs(R,{value:l.type,onValueChange:a=>b("type",a),children:[e.jsx(B,{children:e.jsx(V,{placeholder:"All types"})}),e.jsxs(q,{children:[e.jsx(F,{value:"all",children:"All types"}),G.map(a=>e.jsx(F,{value:a.value,children:a.label},a.value))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"organization",children:"Organization"}),e.jsxs(R,{value:l.organization_id,onValueChange:a=>b("organization_id",a),children:[e.jsx(B,{children:e.jsx(V,{placeholder:"All organizations"})}),e.jsxs(q,{children:[e.jsx(F,{value:"all",children:"All organizations"}),f.map(a=>e.jsx(F,{value:a.id.toString(),children:a.name},a.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"campus",children:"Campus"}),e.jsxs(R,{value:l.campus,onValueChange:a=>b("campus",a),children:[e.jsx(B,{children:e.jsx(V,{placeholder:"All campuses"})}),e.jsxs(q,{children:[e.jsx(F,{value:"all",children:"All campuses"}),ke.map(a=>e.jsx(F,{value:a,children:a},a))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{htmlFor:"sort",children:"Sort By"}),e.jsxs(R,{value:l.sort_by,onValueChange:a=>b("sort_by",a),children:[e.jsx(B,{children:e.jsx(V,{})}),e.jsx(q,{children:Se.map(a=>e.jsx(F,{value:a.value,children:a.label},a.value))})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{children:"Date Range"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx(w,{htmlFor:"date_from",className:"text-xs text-gray-500",children:"From"}),e.jsx(Q,{id:"date_from",type:"date",value:l.date_from,onChange:a=>b("date_from",a.target.value)})]}),e.jsxs("div",{children:[e.jsx(w,{htmlFor:"date_to",className:"text-xs text-gray-500",children:"To"}),e.jsx(Q,{id:"date_to",type:"date",value:l.date_to,onChange:a=>b("date_to",a.target.value)})]})]})]}),e.jsxs("div",{className:"flex justify-between pt-4 border-t",children:[e.jsxs(M,{variant:"outline",onClick:_,className:"flex items-center gap-2",children:[e.jsx(we,{className:"w-4 h-4"}),"Reset All"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(M,{variant:"outline",onClick:i,children:"Cancel"}),e.jsx(M,{onClick:d,children:"Apply Filters"})]})]})]})]})}const Fe=[{value:"announcement",label:"Announcement",icon:"📢"},{value:"discussion",label:"Discussion",icon:"💬"},{value:"event",label:"Event",icon:"📅"},{value:"news",label:"News",icon:"📰"}],Ae=[{value:"public",label:"Public",icon:be,description:"Anyone can see this post"},{value:"members_only",label:"Members Only",icon:H,description:"Only organization members can see this"},{value:"private",label:"Private",icon:ve,description:"Only you can see this post"}];function Oe({onClose:u,onPostCreated:S}){const[i,l]=t.useState({title:"",content:"",type:"announcement",visibility:"public",organization_id:"",is_pinned:!1,comments_enabled:!0,published_at:""}),[v,f]=t.useState([]),[g,L]=t.useState([]),[A,b]=t.useState(!1),[d,_]=t.useState({}),y=t.useRef(null);t.useState(()=>{(async()=>{var r;try{const x=await fetch("/api/v1/organizations?member=true",{headers:{Authorization:`Bearer ${(r=document.querySelector('meta[name="api-token"]'))==null?void 0:r.getAttribute("content")}`,Accept:"application/json"}});if(x.ok){const c=await x.json();L(c.data||[])}}catch(x){console.error("Error loading organizations:",x)}})()});const m=(s,r)=>{l(x=>({...x,[s]:r})),d[s]&&_(x=>({...x,[s]:""}))},C=s=>{const x=Array.from(s.target.files||[]).filter(c=>{const k=c.type.startsWith("image/")||c.type==="application/pdf"||c.type.includes("document"),o=c.size<=10*1024*1024;return k&&o});f(c=>[...c,...x])},$=s=>{f(r=>r.filter((x,c)=>c!==s))},a=()=>{const s={};return i.title.trim()||(s.title="Title is required"),i.content.trim()||(s.content="Content is required"),i.visibility==="members_only"&&!i.organization_id&&(s.organization_id="Organization is required for members-only posts"),_(s),Object.keys(s).length===0},h=async s=>{var r,x;if(s.preventDefault(),!!a()){b(!0);try{const c=new FormData;Object.entries(i).forEach(([o,z])=>{z!==""&&c.append(o,z.toString())}),v.forEach((o,z)=>{c.append(`media[${z}]`,o)});const k=await fetch("/api/v1/posts",{method:"POST",headers:{Authorization:`Bearer ${(r=document.querySelector('meta[name="api-token"]'))==null?void 0:r.getAttribute("content")}`,"X-CSRF-TOKEN":((x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content"))||""},body:c});if(k.ok){const o=await k.json();S(o.post)}else{const o=await k.json();_(o.errors||{general:"Failed to create post"})}}catch(c){console.error("Error creating post:",c),_({general:"Network error. Please try again."})}finally{b(!1)}}},N=g.find(s=>s.id.toString()===i.organization_id);return e.jsx(xe,{open:!0,onOpenChange:u,children:e.jsxs(pe,{className:"unilink-modal-content",children:[e.jsx(je,{className:"sr-only",children:"Create New Post"}),e.jsx("div",{className:"modal-dialog modal-lg",children:e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h5",{className:"modal-title d-flex align-items-center",children:[e.jsx(W,{className:"w-5 h-5 me-2"}),"Create New Post"]}),e.jsx("button",{type:"button",className:"btn-close",onClick:u})]}),e.jsx("div",{className:"modal-body",style:{maxHeight:"70vh",overflowY:"auto"},children:e.jsxs("form",{onSubmit:h,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(w,{className:"form-label",children:"Post Type"}),e.jsx("div",{className:"row g-2",children:Fe.map(s=>e.jsx("div",{className:"col-6",children:e.jsx("button",{type:"button",onClick:()=>m("type",s.value),className:`btn w-100 p-3 text-start ${i.type===s.value?"btn-primary":"btn-outline-secondary"}`,children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("span",{className:"me-2",style:{fontSize:"1.2rem"},children:s.icon}),e.jsx("span",{className:"fw-medium",children:s.label})]})})},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"title",className:"form-label",children:"Title *"}),e.jsx("input",{type:"text",id:"title",className:`form-control ${d.title?"is-invalid":""}`,value:i.title,onChange:s=>m("title",s.target.value),placeholder:"Enter post title..."}),d.title&&e.jsx("div",{className:"invalid-feedback",children:d.title})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"content",className:"form-label",children:"Content *"}),e.jsx("textarea",{id:"content",className:`form-control ${d.content?"is-invalid":""}`,value:i.content,onChange:s=>m("content",s.target.value),placeholder:"What's on your mind?",rows:6}),d.content&&e.jsx("div",{className:"invalid-feedback",children:d.content})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"form-label",children:"Media"}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("button",{type:"button",className:"btn btn-outline-primary w-100",onClick:()=>{var s;return(s=y.current)==null?void 0:s.click()},children:[e.jsx(Ce,{className:"w-4 h-4 me-2"}),"Add Images or Files"]}),e.jsx("input",{ref:y,type:"file",multiple:!0,accept:"image/*,.pdf,.doc,.docx",onChange:C,className:"d-none"}),v.length>0&&e.jsx("div",{className:"row g-2 mt-3",children:v.map((s,r)=>e.jsx("div",{className:"col-6",children:e.jsx("div",{className:"card p-2",children:e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center flex-grow-1 min-w-0",children:[s.type.startsWith("image/")?e.jsx(Ne,{className:"w-4 h-4 text-primary"}):e.jsx(W,{className:"w-4 h-4 text-muted"}),e.jsx("span",{className:"small text-truncate",children:s.name})]}),e.jsx("button",{type:"button",className:"btn btn-sm btn-outline-danger",onClick:()=>$(r),children:e.jsx(ge,{className:"w-4 h-4"})})]})})},r))})]})]}),g.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"form-label",children:"Post as Organization (Optional)"}),e.jsxs("select",{className:`form-select ${d.organization_id?"is-invalid":""}`,value:i.organization_id,onChange:s=>m("organization_id",s.target.value),children:[e.jsx("option",{value:"",children:"Personal Post"}),g.map(s=>e.jsx("option",{value:s.id.toString(),children:s.name},s.id))]}),d.organization_id&&e.jsx("div",{className:"invalid-feedback",children:d.organization_id})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"form-label",children:"Visibility"}),e.jsx("div",{className:"d-grid gap-2",children:Ae.map(s=>{const r=s.icon;return e.jsx("button",{type:"button",onClick:()=>m("visibility",s.value),disabled:s.value==="members_only"&&!i.organization_id,className:`btn p-3 text-start ${i.visibility===s.value?"btn-primary":"btn-outline-secondary"} ${s.value==="members_only"&&!i.organization_id?"disabled":""}`,children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(r,{className:"w-5 h-5 me-3"}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-medium",children:s.label}),e.jsx("div",{className:"small text-muted",children:s.description})]})]})},s.value)})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label mb-0",children:"Enable Comments"}),e.jsx("p",{className:"small text-muted mb-0",children:"Allow users to comment on this post"})]}),e.jsx("div",{className:"form-check form-switch",children:e.jsx("input",{className:"form-check-input",type:"checkbox",id:"comments_enabled",checked:i.comments_enabled,onChange:s=>m("comments_enabled",s.target.checked)})})]}),N&&e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label mb-0",children:"Pin Post"}),e.jsx("p",{className:"small text-muted mb-0",children:"Pin this post to the top of the feed"})]}),e.jsx("div",{className:"form-check form-switch",children:e.jsx("input",{className:"form-check-input",type:"checkbox",id:"is_pinned",checked:i.is_pinned,onChange:s=>m("is_pinned",s.target.checked)})})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"published_at",className:"form-label",children:"Schedule Post (Optional)"}),e.jsx("input",{type:"datetime-local",id:"published_at",className:"form-control",value:i.published_at,onChange:s=>m("published_at",s.target.value),min:new Date().toISOString().slice(0,16)}),e.jsx("div",{className:"form-text",children:"Leave empty to publish immediately"})]}),d.general&&e.jsx("div",{className:"alert alert-danger",children:d.general})]})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",className:"btn btn-secondary",onClick:u,children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:A,onClick:h,children:A?"Creating...":"Create Post"})]})]})})]})})}const Ee=[{title:"Feed",href:"/feed"}];function Je({initialPosts:u,feedType:S="personalized"}){var K;const{props:i}=le(),l=(K=i.auth)==null?void 0:K.api_token,[v,f]=t.useState((u==null?void 0:u.data)||[]),[g,L]=t.useState(!1),[A,b]=t.useState(!1),[d,_]=t.useState(u?u.current_page<u.last_page:!0),[y,m]=t.useState((u==null?void 0:u.current_page)||1),[C,$]=t.useState(""),[a,h]=t.useState(!1),[N,s]=t.useState(!1),[r,x]=t.useState(S);console.log("Feed component props:",{initialPosts:u,feedType:S,posts:v});const[c,k]=t.useState({type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"}),o=t.useCallback(async(n=1,j=!1)=>{L(!0);try{const O=new URLSearchParams({page:n.toString(),per_page:"15",...C&&{search:C},...Object.fromEntries(Object.entries(c).filter(([p,U])=>U!==""))});let E="/api/v1/posts";r==="personalized"?E="/api/v1/feed":r==="trending"&&(E="/api/v1/feed/trending");const D=await fetch(`${E}?${O}`,{headers:{Authorization:`Bearer ${l}`,Accept:"application/json"}});if(D.ok){const p=await D.json();f(j?U=>[...U,...p.data]:p.data),m(p.current_page),_(p.current_page<p.last_page)}}catch(O){console.error("Error loading posts:",O)}finally{L(!1)}},[r,C,c]),z=t.useCallback(()=>{!g&&d&&o(y+1,!0)},[g,d,y,o]);t.useCallback(async()=>{b(!0),await o(1,!1),b(!1)},[o]),t.useCallback(n=>{n.preventDefault(),o(1,!1)},[o]);const Z=t.useCallback(n=>{k(n),o(1,!1)},[o]),ee=t.useCallback(n=>{x(n),m(1),f([])},[]);t.useEffect(()=>{o(1,!1)},[r]),t.useEffect(()=>{const n=()=>{window.innerHeight+document.documentElement.scrollTop!==document.documentElement.offsetHeight||g||z()};return window.addEventListener("scroll",n),()=>window.removeEventListener("scroll",n)},[z,g]);const se=async(n,j)=>{var O;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l}`,"X-CSRF-TOKEN":((O=document.querySelector('meta[name="csrf-token"]'))==null?void 0:O.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:n,type:j})})).ok&&f(D=>D.map(p=>p.id===n?{...p,reactions_count:p.user_reaction===j?p.reactions_count-1:p.reactions_count+(p.user_reaction?0:1),user_reaction:p.user_reaction===j?void 0:j}:p))}catch(E){console.error("Error toggling reaction:",E)}},ae=n=>{window.location.href=`/posts/${n}#comments`},te=n=>{var j;(j=navigator.share)==null||j.call(navigator,{title:"UniLink Post",url:`${window.location.origin}/posts/${n}`})};return e.jsxs(de,{breadcrumbs:Ee,children:[e.jsx(ne,{title:"Feed"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"feed-layout-container",children:e.jsxs("div",{className:"row g-4",children:[e.jsx("div",{className:"col-lg-3 col-md-4 d-none d-md-block",children:e.jsx("div",{className:"sidebar-content",children:e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header bg-unilink-primary text-white",children:e.jsx("h5",{className:"mb-0",children:"Menu"})}),e.jsxs("div",{className:"list-group list-group-flush",children:[e.jsxs(I,{href:"/profile",className:"list-group-item list-group-item-action",children:[e.jsx(H,{className:"w-4 h-4 me-2"}),"Profile"]}),e.jsxs(I,{href:"/organizations",className:"list-group-item list-group-item-action",children:[e.jsx(H,{className:"w-4 h-4 me-2"}),"My Organizations"]}),e.jsxs(I,{href:"/events",className:"list-group-item list-group-item-action",children:[e.jsx(me,{className:"w-4 h-4 me-2"}),"Events"]}),e.jsxs(I,{href:"/settings",className:"list-group-item list-group-item-action",children:[e.jsx(he,{className:"w-4 h-4 me-2"}),"Settings"]})]})]})})}),e.jsxs("div",{className:"col-lg-6 col-md-8 col-12",children:[e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("button",{onClick:()=>ee("personalized"),className:`btn btn-sm me-2 ${r==="personalized"?"btn-primary":"btn-outline-primary"}`,children:"All"}),e.jsxs("button",{onClick:()=>h(!a),className:"btn btn-outline-primary btn-sm",children:[e.jsx(J,{className:"w-4 h-4 me-1"}),"Filter"]})]}),e.jsxs("button",{onClick:()=>s(!0),className:"btn btn-primary btn-sm",children:[e.jsx(X,{className:"w-4 h-4 me-1"}),"Create Post"]})]})})}),a&&e.jsx(ze,{filters:c,onFiltersChange:Z,onClose:()=>h(!1)}),e.jsxs("div",{className:"feed-content",children:[v.length>0?v.map(n=>e.jsx(ue,{post:n,onReact:se,onComment:ae,onShare:te},n.id)):g?null:e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx(W,{className:"w-12 h-12 text-muted mx-auto mb-4"}),e.jsx("h3",{className:"h5 mb-2",children:"No posts found"}),e.jsx("p",{className:"text-muted mb-4",children:"Be the first to add a post!"}),e.jsxs("button",{onClick:()=>s(!0),className:"btn btn-primary",children:[e.jsx(X,{className:"w-4 h-4 me-2"}),"Create Post"]})]})}),g&&e.jsx("div",{children:[...Array(3)].map((n,j)=>e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx("div",{className:"placeholder-glow",children:e.jsx("div",{className:"placeholder rounded-circle me-3",style:{width:"40px",height:"40px"}})}),e.jsxs("div",{className:"placeholder-glow flex-grow-1",children:[e.jsx("div",{className:"placeholder col-4 mb-2"}),e.jsx("div",{className:"placeholder col-3"})]})]}),e.jsxs("div",{className:"placeholder-glow",children:[e.jsx("div",{className:"placeholder col-8 mb-3"}),e.jsx("div",{className:"placeholder col-12 mb-3",style:{height:"80px"}}),e.jsxs("div",{className:"d-flex",children:[e.jsx("div",{className:"placeholder col-2 me-2"}),e.jsx("div",{className:"placeholder col-2 me-2"}),e.jsx("div",{className:"placeholder col-2"})]})]})]})},j))})]})]}),e.jsx("div",{className:"col-lg-3 d-none d-lg-block",children:e.jsxs("div",{className:"sidebar-content",children:[e.jsxs("div",{className:"card shadow-sm mb-4",children:[e.jsx("div",{className:"card-header bg-unilink-primary text-white",children:e.jsx("h5",{className:"mb-0",children:"Upcoming Events"})}),e.jsxs("div",{className:"card-body",children:[e.jsx("div",{className:"small text-muted mb-2",children:"Enrollment Period: Aug 1-15"}),e.jsx("div",{className:"small text-muted mb-2",children:"Orientation Day: Aug 20"}),e.jsx("div",{className:"small text-muted mb-2",children:"First Day of Classes: Aug 22"}),e.jsx("div",{className:"small text-muted",children:"Foundation Day: Sept 15"})]})]}),e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header bg-unilink-primary text-white",children:e.jsx("h5",{className:"mb-0",children:"Quick Links"})}),e.jsxs("div",{className:"list-group list-group-flush",children:[e.jsx("a",{href:"https://sksu.edu.ph/",target:"_blank",rel:"noopener noreferrer",className:"list-group-item list-group-item-action",children:"University Website"}),e.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Student Portal"}),e.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Library Resources"}),e.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Academic Calendar"})]})]})]})})]})})})}),N&&e.jsx(Oe,{onClose:()=>s(!1),onPostCreated:n=>{f(j=>[n,...j]),s(!1)}})]})}export{Je as default};
