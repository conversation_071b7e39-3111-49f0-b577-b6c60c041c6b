import { ReactNode } from 'react';
import { <PERSON> } from '@inertiajs/react';
import { 
    <PERSON>, 
    Calendar, 
    Setting<PERSON>,
    Bell,
    BookOpen,
    MessageCircle,
    TrendingUp
} from 'lucide-react';

interface ThreeColumnLayoutProps {
    children: ReactNode;
    leftSidebar?: ReactNode;
    rightSidebar?: ReactNode;
    showDefaultSidebars?: boolean;
}

const DefaultLeftSidebar = () => (
    <div className="sidebar-content">
        <div className="card shadow-sm">
            <div className="card-header bg-unilink-primary text-white">
                <h5 className="mb-0">Menu</h5>
            </div>
            <div className="list-group list-group-flush">
                <Link href="/profile" className="list-group-item list-group-item-action">
                    <Users className="w-4 h-4 me-2" />
                    Profile
                </Link>
                <Link href="/organizations" className="list-group-item list-group-item-action">
                    <Users className="w-4 h-4 me-2" />
                    My Organizations
                </Link>
                <Link href="/events" className="list-group-item list-group-item-action">
                    <Calendar className="w-4 h-4 me-2" />
                    Events
                </Link>
                <Link href="/courses" className="list-group-item list-group-item-action">
                    <BookOpen className="w-4 h-4 me-2" />
                    Courses
                </Link>
                <Link href="/messages" className="list-group-item list-group-item-action">
                    <MessageCircle className="w-4 h-4 me-2" />
                    Messages
                </Link>
                <Link href="/settings" className="list-group-item list-group-item-action">
                    <Settings className="w-4 h-4 me-2" />
                    Settings
                </Link>
            </div>
        </div>
    </div>
);

const DefaultRightSidebar = () => (
    <div className="sidebar-content">
        {/* Trending Topics */}
        <div className="card shadow-sm mb-4">
            <div className="card-header bg-unilink-primary text-white">
                <h5 className="mb-0 d-flex align-items-center">
                    <TrendingUp className="w-4 h-4 me-2" />
                    Trending
                </h5>
            </div>
            <div className="card-body">
                <div className="small mb-2">
                    <a href="#" className="text-decoration-none">#StudentLife</a>
                </div>
                <div className="small mb-2">
                    <a href="#" className="text-decoration-none">#Academics</a>
                </div>
                <div className="small mb-2">
                    <a href="#" className="text-decoration-none">#CampusEvents</a>
                </div>
                <div className="small">
                    <a href="#" className="text-decoration-none">#Research</a>
                </div>
            </div>
        </div>

        {/* Upcoming Events */}
        <div className="card shadow-sm mb-4">
            <div className="card-header bg-unilink-primary text-white">
                <h5 className="mb-0 d-flex align-items-center">
                    <Calendar className="w-4 h-4 me-2" />
                    Upcoming Events
                </h5>
            </div>
            <div className="card-body">
                <div className="small text-muted mb-2">
                    Enrollment Period: Aug 1-15
                </div>
                <div className="small text-muted mb-2">
                    Orientation Day: Aug 20
                </div>
                <div className="small text-muted mb-2">
                    First Day of Classes: Aug 22
                </div>
                <div className="small text-muted">
                    Foundation Day: Sept 15
                </div>
            </div>
        </div>

        {/* Quick Links */}
        <div className="card shadow-sm">
            <div className="card-header bg-unilink-primary text-white">
                <h5 className="mb-0">Quick Links</h5>
            </div>
            <div className="list-group list-group-flush">
                <a href="https://sksu.edu.ph/" target="_blank" rel="noopener noreferrer" className="list-group-item list-group-item-action">
                    University Website
                </a>
                <a href="#" className="list-group-item list-group-item-action">
                    Student Portal
                </a>
                <a href="#" className="list-group-item list-group-item-action">
                    Library Resources
                </a>
                <a href="#" className="list-group-item list-group-item-action">
                    Academic Calendar
                </a>
            </div>
        </div>
    </div>
);

export default function ThreeColumnLayout({ 
    children, 
    leftSidebar, 
    rightSidebar, 
    showDefaultSidebars = true 
}: ThreeColumnLayoutProps) {
    return (
        <div className="main-container">
            <div className="content-area">
                <div className="container">
                    <div className="three-column-wrapper">
                        <div className="row g-3">
                            {/* Left Sidebar */}
                            <div className="col-lg-3 col-md-3 d-none d-md-block">
                                {leftSidebar || (showDefaultSidebars && <DefaultLeftSidebar />)}
                            </div>

                            {/* Main Content */}
                            <div className="col-lg-6 col-md-6 col-12">
                                <div className="main-content">
                                    {children}
                                </div>
                            </div>

                            {/* Right Sidebar */}
                            <div className="ol-lg-3 col-md-3 d-none d-md-block">
                                {rightSidebar || (showDefaultSidebars && <DefaultRightSidebar />)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
