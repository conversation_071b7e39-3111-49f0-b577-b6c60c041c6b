import{j as t}from"./app-BYdmc_gi.js";import{p as i,T as l,q as r,r as s,X as d,s as c,t as g,v as u,O as f}from"./app-layout-B0AAWw2-.js";import{c as e}from"./app-logo-icon-D4PatI0y.js";function D({...a}){return t.jsx(i,{"data-slot":"dialog",...a})}function b({...a}){return t.jsx(l,{"data-slot":"dialog-trigger",...a})}function m({...a}){return t.jsx(u,{"data-slot":"dialog-portal",...a})}function h({...a}){return t.jsx(s,{"data-slot":"dialog-close",...a})}function x({className:a,...o}){return t.jsx(f,{"data-slot":"dialog-overlay",className:e("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",a),...o})}function y({className:a,children:o,...n}){return t.jsxs(m,{"data-slot":"dialog-portal",children:[t.jsx(x,{}),t.jsxs(r,{"data-slot":"dialog-content",className:e("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...n,children:[o,t.jsxs(s,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[t.jsx(d,{}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function N({className:a,...o}){return t.jsx("div",{"data-slot":"dialog-footer",className:e("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...o})}function z({className:a,...o}){return t.jsx(c,{"data-slot":"dialog-title",className:e("text-lg leading-none font-semibold",a),...o})}function w({className:a,...o}){return t.jsx(g,{"data-slot":"dialog-description",className:e("text-muted-foreground text-sm",a),...o})}export{D,y as a,z as b,b as c,w as d,N as e,h as f};
