import { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
    Image,
    FileText,
    Users,
    Globe,
    Lock,
    Upload,
    Trash2
} from 'lucide-react';

interface Organization {
    id: number;
    name: string;
    logo?: string;
}

interface CreatePostModalProps {
    onClose: () => void;
    onPostCreated: (post: any) => void;
}

const POST_TYPES = [
    { value: 'announcement', label: 'Announcement', icon: '📢' },
    { value: 'discussion', label: 'Discussion', icon: '💬' },
    { value: 'event', label: 'Event', icon: '📅' },
    { value: 'news', label: 'News', icon: '📰' },
];

const VISIBILITY_OPTIONS = [
    { value: 'public', label: 'Public', icon: Globe, description: 'Anyone can see this post' },
    { value: 'members_only', label: 'Members Only', icon: Users, description: 'Only organization members can see this' },
    { value: 'private', label: 'Private', icon: Lock, description: 'Only you can see this post' },
];

export default function CreatePostModal({ onClose, onPostCreated }: CreatePostModalProps) {
    const [formData, setFormData] = useState({
        title: '',
        content: '',
        type: 'announcement',
        visibility: 'public',
        organization_id: '',
        is_pinned: false,
        comments_enabled: true,
        published_at: '',
    });
    
    const [mediaFiles, setMediaFiles] = useState<File[]>([]);
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Load user's organizations
    useState(() => {
        const loadOrganizations = async () => {
            try {
                const response = await fetch('/api/v1/organizations?member=true', {
                    headers: {
                        'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                        'Accept': 'application/json',
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    setOrganizations(data.data || []);
                }
            } catch (error) {
                console.error('Error loading organizations:', error);
            }
        };

        loadOrganizations();
    });

    const handleInputChange = (field: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const validFiles = files.filter(file => {
            const isValidType = file.type.startsWith('image/') || 
                               file.type === 'application/pdf' ||
                               file.type.includes('document');
            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
            return isValidType && isValidSize;
        });
        
        setMediaFiles(prev => [...prev, ...validFiles]);
    };

    const removeFile = (index: number) => {
        setMediaFiles(prev => prev.filter((_, i) => i !== index));
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};
        
        if (!formData.title.trim()) {
            newErrors.title = 'Title is required';
        }
        
        if (!formData.content.trim()) {
            newErrors.content = 'Content is required';
        }
        
        if (formData.visibility === 'members_only' && !formData.organization_id) {
            newErrors.organization_id = 'Organization is required for members-only posts';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        setLoading(true);
        
        try {
            const submitData = new FormData();
            
            // Add form fields
            Object.entries(formData).forEach(([key, value]) => {
                if (value !== '') {
                    submitData.append(key, value.toString());
                }
            });
            
            // Add media files
            mediaFiles.forEach((file, index) => {
                submitData.append(`media[${index}]`, file);
            });
            
            const response = await fetch('/api/v1/posts', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: submitData,
            });
            
            if (response.ok) {
                const data = await response.json();
                onPostCreated(data.post);
            } else {
                const errorData = await response.json();
                setErrors(errorData.errors || { general: 'Failed to create post' });
            }
        } catch (error) {
            console.error('Error creating post:', error);
            setErrors({ general: 'Network error. Please try again.' });
        } finally {
            setLoading(false);
        }
    };

    const selectedOrganization = organizations.find(org => org.id.toString() === formData.organization_id);

    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="modal-dialog modal-lg">
                <div className="modal-content">
                    <div className="modal-header">
                        <h5 className="modal-title d-flex align-items-center">
                            <FileText className="w-5 h-5 me-2" />
                            Create New Post
                        </h5>
                        <button type="button" className="btn-close" onClick={onClose}></button>
                    </div>

                    <div className="modal-body" style={{maxHeight: '70vh', overflowY: 'auto'}}>
                        <form onSubmit={handleSubmit}>
                            {/* Post Type */}
                            <div className="mb-4">
                                <Label className="form-label">Post Type</Label>
                                <div className="row g-2">
                                    {POST_TYPES.map((type) => (
                                        <div key={type.value} className="col-6">
                                            <button
                                                type="button"
                                                onClick={() => handleInputChange('type', type.value)}
                                                className={`btn w-100 p-3 text-start ${
                                                    formData.type === type.value
                                                        ? 'btn-primary'
                                                        : 'btn-outline-secondary'
                                                }`}
                                            >
                                                <div className="d-flex align-items-center">
                                                    <span className="me-2" style={{fontSize: '1.2rem'}}>{type.icon}</span>
                                                    <span className="fw-medium">{type.label}</span>
                                                </div>
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Title */}
                            <div className="mb-4">
                                <label htmlFor="title" className="form-label">Title *</label>
                                <input
                                    type="text"
                                    id="title"
                                    className={`form-control ${errors.title ? 'is-invalid' : ''}`}
                                    value={formData.title}
                                    onChange={(e) => handleInputChange('title', e.target.value)}
                                    placeholder="Enter post title..."
                                />
                                {errors.title && <div className="invalid-feedback">{errors.title}</div>}
                            </div>

                            {/* Content */}
                            <div className="mb-4">
                                <label htmlFor="content" className="form-label">Content *</label>
                                <textarea
                                    id="content"
                                    className={`form-control ${errors.content ? 'is-invalid' : ''}`}
                                    value={formData.content}
                                    onChange={(e) => handleInputChange('content', e.target.value)}
                                    placeholder="What's on your mind?"
                                    rows={6}
                                />
                                {errors.content && <div className="invalid-feedback">{errors.content}</div>}
                            </div>

                            {/* Media Upload */}
                            <div className="mb-4">
                                <label className="form-label">Media</label>
                                <div className="mb-3">
                                    <button
                                        type="button"
                                        className="btn btn-outline-primary w-100"
                                        onClick={() => fileInputRef.current?.click()}
                                    >
                                        <Upload className="w-4 h-4 me-2" />
                                        Add Images or Files
                                    </button>

                                    <input
                                        ref={fileInputRef}
                                        type="file"
                                        multiple
                                        accept="image/*,.pdf,.doc,.docx"
                                        onChange={handleFileSelect}
                                        className="d-none"
                                    />

                                    {mediaFiles.length > 0 && (
                                        <div className="row g-2 mt-3">
                                            {mediaFiles.map((file, index) => (
                                                <div key={index} className="col-6">
                                                    <div className="card p-2">
                                                        <div className="d-flex align-items-center justify-content-between">
                                                            <div className="d-flex align-items-center flex-grow-1 min-w-0">
                                                                {file.type.startsWith('image/') ? (
                                                                        <Image className="w-4 h-4 text-primary" />
                                                                    ) : (
                                                                        <FileText className="w-4 h-4 text-muted" />
                                                                    )}
                                                                    <span className="small text-truncate">{file.name}</span>
                                                                </div>
                                                                <button
                                                                    type="button"
                                                                    className="btn btn-sm btn-outline-danger"
                                                                    onClick={() => removeFile(index)}
                                                                >
                                                                    <Trash2 className="w-4 h-4" />
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Organization */}
                            {organizations.length > 0 && (
                                <div className="mb-4">
                                    <label className="form-label">Post as Organization (Optional)</label>
                                    <select
                                        className={`form-select ${errors.organization_id ? 'is-invalid' : ''}`}
                                        value={formData.organization_id}
                                        onChange={(e) => handleInputChange('organization_id', e.target.value)}
                                    >
                                        <option value="">Personal Post</option>
                                        {organizations.map((org) => (
                                            <option key={org.id} value={org.id.toString()}>
                                                {org.name}
                                            </option>
                                        ))}
                                    </select>
                                    {errors.organization_id && <div className="invalid-feedback">{errors.organization_id}</div>}
                                </div>
                            )}

                            {/* Visibility */}
                            <div className="mb-4">
                                <label className="form-label">Visibility</label>
                                <div className="d-grid gap-2">
                                    {VISIBILITY_OPTIONS.map((option) => {
                                        const Icon = option.icon;
                                        return (
                                            <button
                                                key={option.value}
                                                type="button"
                                                onClick={() => handleInputChange('visibility', option.value)}
                                                disabled={option.value === 'members_only' && !formData.organization_id}
                                                className={`btn p-3 text-start ${
                                                    formData.visibility === option.value
                                                        ? 'btn-primary'
                                                        : 'btn-outline-secondary'
                                                } ${option.value === 'members_only' && !formData.organization_id ? 'disabled' : ''}`}
                                            >
                                                <div className="d-flex align-items-center">
                                                    <Icon className="w-5 h-5 me-3" />
                                                    <div>
                                                        <div className="fw-medium">{option.label}</div>
                                                        <div className="small text-muted">{option.description}</div>
                                                    </div>
                                                </div>
                                            </button>
                                        );
                                    })}
                                </div>
                            </div>

                            {/* Options */}
                            <div className="mb-4">
                                <div className="d-flex align-items-center justify-content-between mb-3">
                                    <div>
                                        <label className="form-label mb-0">Enable Comments</label>
                                        <p className="small text-muted mb-0">Allow users to comment on this post</p>
                                    </div>
                                    <div className="form-check form-switch">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            id="comments_enabled"
                                            checked={formData.comments_enabled}
                                            onChange={(e) => handleInputChange('comments_enabled', e.target.checked)}
                                        />
                                    </div>
                                </div>

                                {selectedOrganization && (
                                    <div className="d-flex align-items-center justify-content-between">
                                        <div>
                                            <label className="form-label mb-0">Pin Post</label>
                                            <p className="small text-muted mb-0">Pin this post to the top of the feed</p>
                                        </div>
                                        <div className="form-check form-switch">
                                            <input
                                                className="form-check-input"
                                                type="checkbox"
                                                id="is_pinned"
                                                checked={formData.is_pinned}
                                                onChange={(e) => handleInputChange('is_pinned', e.target.checked)}
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Schedule */}
                            <div className="mb-4">
                                <label htmlFor="published_at" className="form-label">Schedule Post (Optional)</label>
                                <input
                                    type="datetime-local"
                                    id="published_at"
                                    className="form-control"
                                    value={formData.published_at}
                                    onChange={(e) => handleInputChange('published_at', e.target.value)}
                                    min={new Date().toISOString().slice(0, 16)}
                                />
                                <div className="form-text">Leave empty to publish immediately</div>
                            </div>

                            {/* Error Message */}
                            {errors.general && (
                                <div className="alert alert-danger">
                                    {errors.general}
                                </div>
                            )}
                        </form>
                    </div>

                    <div className="modal-footer">
                        <button type="button" className="btn btn-secondary" onClick={onClose}>
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="btn btn-primary"
                            disabled={loading}
                            onClick={handleSubmit}
                        >
                            {loading ? 'Creating...' : 'Create Post'}
                        </button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
