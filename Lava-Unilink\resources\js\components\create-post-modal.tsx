import { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
    X, 
    Image, 
    FileText, 
    Calendar, 
    Users, 
    Globe, 
    Lock, 
    Eye,
    Upload,
    Trash2
} from 'lucide-react';

interface Organization {
    id: number;
    name: string;
    logo?: string;
}

interface CreatePostModalProps {
    onClose: () => void;
    onPostCreated: (post: any) => void;
}

const POST_TYPES = [
    { value: 'announcement', label: 'Announcement', icon: '📢' },
    { value: 'discussion', label: 'Discussion', icon: '💬' },
    { value: 'event', label: 'Event', icon: '📅' },
    { value: 'news', label: 'News', icon: '📰' },
];

const VISIBILITY_OPTIONS = [
    { value: 'public', label: 'Public', icon: Globe, description: 'Anyone can see this post' },
    { value: 'members_only', label: 'Members Only', icon: Users, description: 'Only organization members can see this' },
    { value: 'private', label: 'Private', icon: Lock, description: 'Only you can see this post' },
];

export default function CreatePostModal({ onClose, onPostCreated }: CreatePostModalProps) {
    const [formData, setFormData] = useState({
        title: '',
        content: '',
        type: 'announcement',
        visibility: 'public',
        organization_id: '',
        is_pinned: false,
        comments_enabled: true,
        published_at: '',
    });
    
    const [mediaFiles, setMediaFiles] = useState<File[]>([]);
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Load user's organizations
    useState(() => {
        const loadOrganizations = async () => {
            try {
                const response = await fetch('/api/v1/organizations?member=true', {
                    headers: {
                        'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                        'Accept': 'application/json',
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    setOrganizations(data.data || []);
                }
            } catch (error) {
                console.error('Error loading organizations:', error);
            }
        };

        loadOrganizations();
    });

    const handleInputChange = (field: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const validFiles = files.filter(file => {
            const isValidType = file.type.startsWith('image/') || 
                               file.type === 'application/pdf' ||
                               file.type.includes('document');
            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
            return isValidType && isValidSize;
        });
        
        setMediaFiles(prev => [...prev, ...validFiles]);
    };

    const removeFile = (index: number) => {
        setMediaFiles(prev => prev.filter((_, i) => i !== index));
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};
        
        if (!formData.title.trim()) {
            newErrors.title = 'Title is required';
        }
        
        if (!formData.content.trim()) {
            newErrors.content = 'Content is required';
        }
        
        if (formData.visibility === 'members_only' && !formData.organization_id) {
            newErrors.organization_id = 'Organization is required for members-only posts';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        setLoading(true);
        
        try {
            const submitData = new FormData();
            
            // Add form fields
            Object.entries(formData).forEach(([key, value]) => {
                if (value !== '') {
                    submitData.append(key, value.toString());
                }
            });
            
            // Add media files
            mediaFiles.forEach((file, index) => {
                submitData.append(`media[${index}]`, file);
            });
            
            const response = await fetch('/api/v1/posts', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: submitData,
            });
            
            if (response.ok) {
                const data = await response.json();
                onPostCreated(data.post);
            } else {
                const errorData = await response.json();
                setErrors(errorData.errors || { general: 'Failed to create post' });
            }
        } catch (error) {
            console.error('Error creating post:', error);
            setErrors({ general: 'Network error. Please try again.' });
        } finally {
            setLoading(false);
        }
    };

    const selectedOrganization = organizations.find(org => org.id.toString() === formData.organization_id);

    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Create New Post
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Post Type */}
                    <div className="space-y-2">
                        <Label>Post Type</Label>
                        <div className="grid grid-cols-2 gap-2">
                            {POST_TYPES.map((type) => (
                                <button
                                    key={type.value}
                                    type="button"
                                    onClick={() => handleInputChange('type', type.value)}
                                    className={`p-3 rounded-lg border text-left transition-colors ${
                                        formData.type === type.value
                                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                                            : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                >
                                    <div className="flex items-center gap-2">
                                        <span className="text-lg">{type.icon}</span>
                                        <span className="font-medium">{type.label}</span>
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Title */}
                    <div className="space-y-2">
                        <Label htmlFor="title">Title *</Label>
                        <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            placeholder="Enter post title..."
                            className={errors.title ? 'border-red-500' : ''}
                        />
                        {errors.title && <p className="text-sm text-red-600">{errors.title}</p>}
                    </div>

                    {/* Content */}
                    <div className="space-y-2">
                        <Label htmlFor="content">Content *</Label>
                        <Textarea
                            id="content"
                            value={formData.content}
                            onChange={(e) => handleInputChange('content', e.target.value)}
                            placeholder="What's on your mind?"
                            rows={6}
                            className={errors.content ? 'border-red-500' : ''}
                        />
                        {errors.content && <p className="text-sm text-red-600">{errors.content}</p>}
                    </div>

                    {/* Media Upload */}
                    <div className="space-y-2">
                        <Label>Media</Label>
                        <div className="space-y-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => fileInputRef.current?.click()}
                                className="w-full"
                            >
                                <Upload className="w-4 h-4 mr-2" />
                                Add Images or Files
                            </Button>
                            
                            <input
                                ref={fileInputRef}
                                type="file"
                                multiple
                                accept="image/*,.pdf,.doc,.docx"
                                onChange={handleFileSelect}
                                className="hidden"
                            />
                            
                            {mediaFiles.length > 0 && (
                                <div className="grid grid-cols-2 gap-2">
                                    {mediaFiles.map((file, index) => (
                                        <Card key={index} className="p-2">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2 min-w-0">
                                                    {file.type.startsWith('image/') ? (
                                                        <Image className="w-4 h-4 text-blue-500" />
                                                    ) : (
                                                        <FileText className="w-4 h-4 text-gray-500" />
                                                    )}
                                                    <span className="text-sm truncate">{file.name}</span>
                                                </div>
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => removeFile(index)}
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Organization */}
                    {organizations.length > 0 && (
                        <div className="space-y-2">
                            <Label>Post as Organization (Optional)</Label>
                            <Select
                                value={formData.organization_id}
                                onValueChange={(value) => handleInputChange('organization_id', value)}
                            >
                                <SelectTrigger className={errors.organization_id ? 'border-red-500' : ''}>
                                    <SelectValue placeholder="Select organization..." />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="personal">Personal Post</SelectItem>
                                    {organizations.map((org) => (
                                        <SelectItem key={org.id} value={org.id.toString()}>
                                            {org.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.organization_id && <p className="text-sm text-red-600">{errors.organization_id}</p>}
                        </div>
                    )}

                    {/* Visibility */}
                    <div className="space-y-2">
                        <Label>Visibility</Label>
                        <div className="space-y-2">
                            {VISIBILITY_OPTIONS.map((option) => {
                                const Icon = option.icon;
                                return (
                                    <button
                                        key={option.value}
                                        type="button"
                                        onClick={() => handleInputChange('visibility', option.value)}
                                        disabled={option.value === 'members_only' && !formData.organization_id}
                                        className={`w-full p-3 rounded-lg border text-left transition-colors ${
                                            formData.visibility === option.value
                                                ? 'border-blue-500 bg-blue-50'
                                                : 'border-gray-200 hover:border-gray-300'
                                        } ${option.value === 'members_only' && !formData.organization_id ? 'opacity-50 cursor-not-allowed' : ''}`}
                                    >
                                        <div className="flex items-center gap-3">
                                            <Icon className="w-5 h-5" />
                                            <div>
                                                <div className="font-medium">{option.label}</div>
                                                <div className="text-sm text-gray-500">{option.description}</div>
                                            </div>
                                        </div>
                                    </button>
                                );
                            })}
                        </div>
                    </div>

                    {/* Options */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <Label htmlFor="comments_enabled">Enable Comments</Label>
                                <p className="text-sm text-gray-500">Allow users to comment on this post</p>
                            </div>
                            <Switch
                                id="comments_enabled"
                                checked={formData.comments_enabled}
                                onCheckedChange={(checked) => handleInputChange('comments_enabled', checked)}
                            />
                        </div>

                        {selectedOrganization && (
                            <div className="flex items-center justify-between">
                                <div>
                                    <Label htmlFor="is_pinned">Pin Post</Label>
                                    <p className="text-sm text-gray-500">Pin this post to the top of the feed</p>
                                </div>
                                <Switch
                                    id="is_pinned"
                                    checked={formData.is_pinned}
                                    onCheckedChange={(checked) => handleInputChange('is_pinned', checked)}
                                />
                            </div>
                        )}
                    </div>

                    {/* Schedule */}
                    <div className="space-y-2">
                        <Label htmlFor="published_at">Schedule Post (Optional)</Label>
                        <Input
                            id="published_at"
                            type="datetime-local"
                            value={formData.published_at}
                            onChange={(e) => handleInputChange('published_at', e.target.value)}
                            min={new Date().toISOString().slice(0, 16)}
                        />
                        <p className="text-sm text-gray-500">Leave empty to publish immediately</p>
                    </div>

                    {/* Error Message */}
                    {errors.general && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-sm text-red-600">{errors.general}</p>
                        </div>
                    )}

                    {/* Actions */}
                    <div className="flex justify-end gap-3 pt-4 border-t">
                        <Button type="button" variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={loading}>
                            {loading ? 'Creating...' : 'Create Post'}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}
