import{r as n,j as e,L as w,$ as m}from"./app-FvBJISK9.js";import{A as S,U as k,C as A,a as _}from"./app-layout-BobmQngD.js";import{B as N}from"./book-open-DA8A2NRJ.js";import{S as F}from"./search-C9xlgvAH.js";import{F as E}from"./filter-RgG-B9-k.js";import{M as $}from"./map-pin-WdguwR6R.js";import{S as O}from"./star-CN9CciN1.js";import"./app-logo-icon-CT9-NZ3f.js";import"./index-CJBSpWUr.js";import"./index-DBDRXzgx.js";const B=[{title:"Courses",href:"/courses"}];function Q(){const[t,b]=n.useState(null),[v,h]=n.useState(!0),[c,u]=n.useState(""),[x,g]=n.useState(!1),[a,i]=n.useState({department:"",campus:"",semester:"",year:"",status:"active",enrolled_only:!1}),o=async(s=1)=>{var l;h(!0);try{const r=new URLSearchParams({page:s.toString(),per_page:"12",...c&&{search:c},...Object.fromEntries(Object.entries(a).filter(([p,j])=>j!==""&&j!==!1))}),d=await fetch(`/api/v1/courses?${r}`,{headers:{Authorization:`Bearer ${(l=document.querySelector('meta[name="api-token"]'))==null?void 0:l.getAttribute("content")}`,Accept:"application/json"}});if(d.ok){const p=await d.json();b(p)}}catch(r){console.error("Error loading courses:",r)}finally{h(!1)}},f=s=>{s.preventDefault(),o(1)},y=async s=>{var l,r;try{(await fetch(`/api/v1/courses/${s}/enroll`,{method:"POST",headers:{Authorization:`Bearer ${(l=document.querySelector('meta[name="api-token"]'))==null?void 0:l.getAttribute("content")}`,"X-CSRF-TOKEN":((r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content"))||""}})).ok?o((t==null?void 0:t.current_page)||1):alert("Failed to enroll in course")}catch(d){console.error("Error enrolling in course:",d),alert("An error occurred while enrolling")}},C=s=>s.status==="full"?e.jsx("span",{className:"badge bg-danger",children:"Full"}):s.status==="inactive"?e.jsx("span",{className:"badge bg-secondary",children:"Inactive"}):s.is_enrolled?e.jsx("span",{className:"badge bg-success",children:"Enrolled"}):e.jsx("span",{className:"badge bg-primary",children:"Available"});return n.useEffect(()=>{o()},[]),e.jsxs(S,{breadcrumbs:B,children:[e.jsx(w,{title:"Courses"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsxs("div",{className:"container mt-4",children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[e.jsx("h1",{className:"h3 mb-0",children:"Courses"}),e.jsx("div",{className:"d-flex gap-2",children:e.jsxs(m,{href:"/courses/my",className:"btn btn-outline-primary",children:[e.jsx(N,{className:"w-4 h-4 me-2"}),"My Courses"]})})]}),e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("form",{onSubmit:f,className:"d-flex gap-2 mb-3",children:[e.jsx("div",{className:"flex-grow-1",children:e.jsx("input",{type:"text",className:"form-control",placeholder:"Search courses by title, code, or instructor...",value:c,onChange:s=>u(s.target.value)})}),e.jsx("button",{type:"submit",className:"btn btn-outline-primary",children:e.jsx(F,{className:"w-4 h-4"})}),e.jsx("button",{type:"button",onClick:()=>g(!x),className:"btn btn-outline-secondary",children:e.jsx(E,{className:"w-4 h-4"})})]}),x&&e.jsx("div",{className:"border-top pt-3",children:e.jsxs("div",{className:"row g-3",children:[e.jsx("div",{className:"col-md-3",children:e.jsxs("select",{className:"form-select",value:a.department,onChange:s=>i({...a,department:s.target.value}),children:[e.jsx("option",{value:"",children:"All Departments"}),e.jsx("option",{value:"Computer Science",children:"Computer Science"}),e.jsx("option",{value:"Engineering",children:"Engineering"}),e.jsx("option",{value:"Business",children:"Business"}),e.jsx("option",{value:"Arts",children:"Arts"})]})}),e.jsx("div",{className:"col-md-3",children:e.jsxs("select",{className:"form-select",value:a.campus,onChange:s=>i({...a,campus:s.target.value}),children:[e.jsx("option",{value:"",children:"All Campuses"}),e.jsx("option",{value:"Main Campus",children:"Main Campus"}),e.jsx("option",{value:"North Campus",children:"North Campus"}),e.jsx("option",{value:"South Campus",children:"South Campus"})]})}),e.jsx("div",{className:"col-md-3",children:e.jsxs("select",{className:"form-select",value:a.semester,onChange:s=>i({...a,semester:s.target.value}),children:[e.jsx("option",{value:"",children:"All Semesters"}),e.jsx("option",{value:"Fall",children:"Fall"}),e.jsx("option",{value:"Spring",children:"Spring"}),e.jsx("option",{value:"Summer",children:"Summer"})]})}),e.jsx("div",{className:"col-md-3",children:e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"enrolled_only",className:"form-check-input",checked:a.enrolled_only,onChange:s=>i({...a,enrolled_only:s.target.checked})}),e.jsx("label",{htmlFor:"enrolled_only",className:"form-check-label",children:"My Courses Only"})]})})]})})]})}),v?e.jsxs("div",{className:"text-center py-5",children:[e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"mt-3 text-muted",children:"Loading courses..."})]}):t&&t.data.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"row g-4",children:t.data.map(s=>e.jsx("div",{className:"col-md-6 col-lg-4",children:e.jsxs("div",{className:"card h-100 shadow-sm",children:[e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[e.jsx("h5",{className:"card-title mb-0",children:e.jsx(m,{href:`/courses/${s.id}`,className:"text-decoration-none",children:s.title})}),C(s)]}),e.jsxs("p",{className:"text-muted small mb-2",children:[s.code," • ",s.credits," Credits"]}),e.jsx("p",{className:"card-text small text-muted mb-3",children:s.description.length>100?s.description.substring(0,100)+"...":s.description}),e.jsxs("div",{className:"small text-muted mb-3",children:[e.jsxs("div",{className:"d-flex align-items-center mb-1",children:[e.jsx(k,{className:"w-3 h-3 me-1"}),e.jsx("span",{children:s.instructor.name})]}),e.jsxs("div",{className:"d-flex align-items-center mb-1",children:[e.jsx(A,{className:"w-3 h-3 me-1"}),e.jsxs("span",{children:[s.semester," ",s.year]})]}),e.jsxs("div",{className:"d-flex align-items-center mb-1",children:[e.jsx(_,{className:"w-3 h-3 me-1"}),e.jsx("span",{children:s.schedule})]}),e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx($,{className:"w-3 h-3 me-1"}),e.jsxs("span",{children:[s.room," • ",s.campus]})]})]}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(O,{className:"w-4 h-4 text-warning me-1"}),e.jsx("span",{className:"small",children:s.rating.toFixed(1)})]}),e.jsxs("small",{className:"text-muted",children:[s.enrolled_count,"/",s.max_capacity," enrolled"]})]})]}),e.jsx("div",{className:"card-footer bg-light",children:s.is_enrolled?e.jsx(m,{href:`/courses/${s.id}`,className:"btn btn-outline-primary w-100",children:"View Course"}):s.status==="full"?e.jsx("button",{className:"btn btn-secondary w-100",disabled:!0,children:"Course Full"}):s.status==="inactive"?e.jsx("button",{className:"btn btn-secondary w-100",disabled:!0,children:"Not Available"}):e.jsx("button",{onClick:()=>y(s.id),className:"btn btn-primary w-100",children:"Enroll Now"})})]})},s.id))}),t.last_page>1&&e.jsx("nav",{className:"mt-4",children:e.jsx("div",{className:"d-flex justify-content-center",children:e.jsx("div",{className:"btn-group",children:Array.from({length:t.last_page},(s,l)=>l+1).map(s=>e.jsx("button",{onClick:()=>o(s),className:`btn ${s===t.current_page?"btn-primary":"btn-outline-primary"}`,children:s},s))})})})]}):e.jsx("div",{className:"card shadow-sm",children:e.jsxs("div",{className:"card-body text-center py-5",children:[e.jsx(N,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h3",{className:"h5 mb-2",children:"No courses found"}),e.jsx("p",{className:"text-muted mb-4",children:c||Object.values(a).some(s=>s)?"Try adjusting your search or filters.":"No courses are currently available."}),(c||Object.values(a).some(s=>s))&&e.jsx("button",{onClick:()=>{u(""),i({department:"",campus:"",semester:"",year:"",status:"active",enrolled_only:!1}),o(1)},className:"btn btn-outline-primary",children:"Clear Filters"})]})})]})})})]})}export{Q as default};
