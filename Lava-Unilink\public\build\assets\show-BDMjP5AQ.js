import{r as c,j as e,L as j,$ as m}from"./app-C5rOGN_d.js";import{A as p,C as S,S as U,U as q}from"./app-layout-RNSP18W5.js";import{P as B}from"./post-card-DDR1-U_h.js";import{B as k}from"./book-open-DnI6DDKa.js";import{M as R}from"./map-pin-DfgKCEP-.js";import{G as M}from"./globe-qyLf3yAP.js";import{S as T}from"./square-pen-Dz_62XDZ.js";import{U as D}from"./user-plus-DLCBKSn5.js";import{M as Y}from"./message-circle-DA4ZcuOf.js";import"./app-logo-icon-DIRi9GnE.js";import"./index-BgpYvdjj.js";import"./index-C4DufK0h.js";import"./pin-r_Zu4Wz2.js";function ae({userId:o,user:h,posts:N}){const[s,f]=c.useState(h||null),[v,b]=c.useState(N||[]),[A,g]=c.useState(!h),[$,w]=c.useState(!1),[y,d]=c.useState(null),[l,x]=c.useState("posts"),u=[{title:"Profile",href:o?`/profile/${o}`:"/profile"}];c.useEffect(()=>{h||P(),N||C()},[o]);const P=async()=>{var t;g(!0),d(null);try{const a=o?`/api/v1/users/${o}`:"/api/v1/user/profile",r=await fetch(a,{headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,Accept:"application/json"}});if(r.ok){const n=await r.json();f(n)}else r.status===404?d("User not found"):d("Failed to load profile")}catch(a){console.error("Error loading user:",a),d("An unexpected error occurred")}finally{g(!1)}},C=async()=>{var t;w(!0);try{const a=o?`/api/v1/users/${o}/posts`:"/api/v1/user/posts",r=await fetch(a,{headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,Accept:"application/json"}});if(r.ok){const n=await r.json();b(n.data||[])}}catch(a){console.error("Error loading user posts:",a)}finally{w(!1)}},z=async()=>{var t,a;if(!(!s||s.is_own_profile))try{(await fetch(`/api/v1/users/${s.id}/follow`,{method:"POST",headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,"X-CSRF-TOKEN":((a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content"))||""}})).ok&&f(n=>n?{...n,is_following:!n.is_following,followers_count:n.is_following?n.followers_count-1:n.followers_count+1}:null)}catch(r){console.error("Error following user:",r)}},E=async(t,a)=>{var r,n;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(r=document.querySelector('meta[name="api-token"]'))==null?void 0:r.getAttribute("content")}`,"X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:t,type:a})})).ok&&b(O=>O.map(i=>i.id===t?{...i,reactions_count:i.user_reaction===a?i.reactions_count-1:i.reactions_count+(i.user_reaction?0:1),user_reaction:i.user_reaction===a?void 0:a}:i))}catch(_){console.error("Error toggling reaction:",_)}},L=t=>{window.location.href=`/posts/${t}#comments`},F=t=>{var a;(a=navigator.share)==null||a.call(navigator,{title:"UniLink Post",url:`${window.location.origin}/posts/${t}`})};return A?e.jsxs(p,{breadcrumbs:u,children:[e.jsx(j,{title:"Loading..."}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsxs("div",{className:"text-center py-5",children:[e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"mt-3 text-muted",children:"Loading profile..."})]})})})})]}):y||!s?e.jsxs(p,{breadcrumbs:u,children:[e.jsx(j,{title:"Error"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"card shadow-sm",children:e.jsxs("div",{className:"card-body text-center py-5",children:[e.jsx("h3",{className:"h5 mb-3",children:"Profile Not Found"}),e.jsx("p",{className:"text-muted mb-4",children:y||"The profile you are looking for does not exist."}),e.jsx(m,{href:"/dashboard",className:"btn btn-primary",children:"Back to Dashboard"})]})})})})})]}):e.jsxs(p,{breadcrumbs:u,children:[e.jsx(j,{title:s.name}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsxs("div",{className:"container mt-4",children:[e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-md-3 text-center",children:e.jsx("img",{src:s.avatar||"/img/profilepic.jpg",alt:s.name,className:"rounded-circle mb-3",width:"120",height:"120",style:{objectFit:"cover"}})}),e.jsxs("div",{className:"col-md-6",children:[e.jsx("h1",{className:"h3 mb-2",children:s.name}),s.student_id&&e.jsxs("p",{className:"text-muted mb-2",children:["Student ID: ",s.student_id]}),s.bio&&e.jsx("p",{className:"text-muted mb-3",children:s.bio}),e.jsxs("div",{className:"row g-2 text-muted small",children:[s.department&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(k,{className:"w-4 h-4 me-1"}),s.department]}),s.location&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(R,{className:"w-4 h-4 me-1"}),s.location]}),e.jsxs("div",{className:"col-md-6",children:[e.jsx(S,{className:"w-4 h-4 me-1"}),"Joined ",new Date(s.joined_at).toLocaleDateString()]}),s.website&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(M,{className:"w-4 h-4 me-1"}),e.jsx("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"text-decoration-none",children:"Website"})]})]})]}),e.jsx("div",{className:"col-md-3 text-center",children:s.is_own_profile?e.jsxs("div",{className:"d-flex flex-column gap-2",children:[e.jsxs(m,{href:"/profile/edit",className:"btn btn-outline-primary",children:[e.jsx(T,{className:"w-4 h-4 me-2"}),"Edit Profile"]}),e.jsxs(m,{href:"/settings",className:"btn btn-outline-secondary",children:[e.jsx(U,{className:"w-4 h-4 me-2"}),"Settings"]})]}):e.jsxs("div",{className:"d-flex flex-column gap-2",children:[e.jsxs("button",{onClick:z,className:`btn ${s.is_following?"btn-outline-primary":"btn-primary"}`,children:[e.jsx(D,{className:"w-4 h-4 me-2"}),s.is_following?"Unfollow":"Follow"]}),e.jsxs("button",{className:"btn btn-outline-secondary",children:[e.jsx(Y,{className:"w-4 h-4 me-2"}),"Message"]})]})})]}),e.jsxs("div",{className:"row text-center mt-4 pt-3 border-top",children:[e.jsxs("div",{className:"col-3",children:[e.jsx("div",{className:"h5 mb-0",children:s.posts_count}),e.jsx("small",{className:"text-muted",children:"Posts"})]}),e.jsxs("div",{className:"col-3",children:[e.jsx("div",{className:"h5 mb-0",children:s.followers_count}),e.jsx("small",{className:"text-muted",children:"Followers"})]}),e.jsxs("div",{className:"col-3",children:[e.jsx("div",{className:"h5 mb-0",children:s.following_count}),e.jsx("small",{className:"text-muted",children:"Following"})]}),e.jsxs("div",{className:"col-3",children:[e.jsx("div",{className:"h5 mb-0",children:s.organizations_count}),e.jsx("small",{className:"text-muted",children:"Organizations"})]})]})]})}),e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsxs("ul",{className:"nav nav-tabs card-header-tabs",children:[e.jsx("li",{className:"nav-item",children:e.jsxs("button",{className:`nav-link ${l==="posts"?"active":""}`,onClick:()=>x("posts"),children:["Posts (",s.posts_count,")"]})}),e.jsx("li",{className:"nav-item",children:e.jsxs("button",{className:`nav-link ${l==="organizations"?"active":""}`,onClick:()=>x("organizations"),children:["Organizations (",s.organizations_count,")"]})}),s.is_own_profile&&e.jsx("li",{className:"nav-item",children:e.jsx("button",{className:`nav-link ${l==="activity"?"active":""}`,onClick:()=>x("activity"),children:"Activity"})})]})}),e.jsxs("div",{className:"card-body",children:[l==="posts"&&e.jsx("div",{children:$?e.jsx("div",{className:"text-center py-4",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})}):v.length>0?e.jsx("div",{className:"space-y-4",children:v.map(t=>e.jsx(B,{post:t,onReact:E,onComment:L,onShare:F},t.id))}):e.jsxs("div",{className:"text-center py-5",children:[e.jsx(k,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h5",{className:"mb-2",children:"No posts yet"}),e.jsx("p",{className:"text-muted",children:s.is_own_profile?"You haven't created any posts yet.":`${s.name} hasn't posted anything yet.`}),s.is_own_profile&&e.jsx(m,{href:"/posts/create",className:"btn btn-primary mt-3",children:"Create Your First Post"})]})}),l==="organizations"&&e.jsxs("div",{className:"text-center py-5",children:[e.jsx(q,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h5",{className:"mb-2",children:"Organizations"}),e.jsx("p",{className:"text-muted",children:"Organization memberships will be shown here."})]}),l==="activity"&&s.is_own_profile&&e.jsxs("div",{className:"text-center py-5",children:[e.jsx(S,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h5",{className:"mb-2",children:"Recent Activity"}),e.jsx("p",{className:"text-muted",children:"Your recent activity will be shown here."})]})]})]})]})})})]})}export{ae as default};
