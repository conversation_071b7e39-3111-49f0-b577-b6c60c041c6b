import{r as l,j as e,L as U,$ as u}from"./app-C5rOGN_d.js";import{A as H,b as v,e as y,B as j,C as O,U as b}from"./app-layout-RNSP18W5.js";import{B as c}from"./app-logo-icon-DIRi9GnE.js";import{I as G}from"./input-CW4FxrAw.js";import{S,a as C,b as P,c as k,d as r}from"./select-CI3Tr4Fo.js";import{P as _}from"./plus-CU5CRObY.js";import{S as I}from"./search-C35SG00j.js";import{P as A,H as K}from"./pin-r_Zu4Wz2.js";import{M as Q}from"./message-circle-DA4ZcuOf.js";import{E as X}from"./eye-B6FMNj4L.js";import{S as Y}from"./square-pen-Dz_62XDZ.js";import{T as J}from"./trash-2-KFo0uwKB.js";import{G as E}from"./globe-qyLf3yAP.js";import{L as W}from"./lock-BR1T7nLo.js";import"./index-BgpYvdjj.js";import"./index-C4DufK0h.js";import"./index-C56EOFHq.js";const Z=[{title:"Posts",href:"/posts"},{title:"Manage",href:"/posts/manage"}];function ge(){const[d,p]=l.useState([]),[z,w]=l.useState(!0),[m,T]=l.useState(""),[h,L]=l.useState("all"),[x,$]=l.useState("all"),[o,B]=l.useState(1),[g,D]=l.useState(1),f=async(s=1)=>{var a;w(!0);try{const t=new URLSearchParams({page:s.toString(),per_page:"10",user_id:"current",...m&&{search:m},...h!=="all"&&{status:h},...x!=="all"&&{type:x}}),n=await fetch(`/api/v1/posts?${t}`,{headers:{Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,Accept:"application/json"}});if(n.ok){const i=await n.json();p(i.data),B(i.current_page),D(i.last_page)}}catch(t){console.error("Error loading posts:",t)}finally{w(!1)}};l.useEffect(()=>{f(1)},[m,h,x]);const F=async s=>{var a,t;if(confirm("Are you sure you want to delete this post?"))try{(await fetch(`/api/v1/posts/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,"X-CSRF-TOKEN":((t=document.querySelector('meta[name="csrf-token"]'))==null?void 0:t.getAttribute("content"))||""}})).ok&&p(d.filter(i=>i.id!==s))}catch(n){console.error("Error deleting post:",n)}},M=async(s,a)=>{var t,n;try{(await fetch(`/api/v1/posts/${s}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,"X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""},body:JSON.stringify({is_pinned:!a})})).ok&&p(d.map(N=>N.id===s?{...N,is_pinned:!a}:N))}catch(i){console.error("Error updating post:",i)}},q=s=>s.published_at&&new Date(s.published_at)>new Date?e.jsx(j,{variant:"outline",children:"Scheduled"}):s.published_at?e.jsx(j,{variant:"default",children:"Published"}):e.jsx(j,{variant:"secondary",children:"Draft"}),V=s=>{switch(s){case"public":return e.jsx(E,{className:"w-4 h-4"});case"members_only":return e.jsx(b,{className:"w-4 h-4"});case"private":return e.jsx(W,{className:"w-4 h-4"});default:return e.jsx(E,{className:"w-4 h-4"})}},R=(s,a=100)=>{const t=s.replace(/<[^>]*>/g,"");return t.length>a?t.substring(0,a)+"...":t};return e.jsxs(H,{breadcrumbs:Z,children:[e.jsx(U,{title:"Manage Posts"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Manage Posts"}),e.jsx("p",{className:"text-gray-600",children:"Create, edit, and manage your posts"})]}),e.jsx(u,{href:"/posts/create",children:e.jsxs(c,{children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),"Create Post"]})})]}),e.jsx(v,{children:e.jsx(y,{className:"p-4",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(I,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx(G,{type:"text",placeholder:"Search posts...",value:m,onChange:s=>T(s.target.value),className:"pl-10"})]}),e.jsxs(S,{value:h,onValueChange:L,children:[e.jsx(C,{className:"w-40",children:e.jsx(P,{placeholder:"Status"})}),e.jsxs(k,{children:[e.jsx(r,{value:"all",children:"All Status"}),e.jsx(r,{value:"published",children:"Published"}),e.jsx(r,{value:"draft",children:"Draft"}),e.jsx(r,{value:"scheduled",children:"Scheduled"})]})]}),e.jsxs(S,{value:x,onValueChange:$,children:[e.jsx(C,{className:"w-40",children:e.jsx(P,{placeholder:"Type"})}),e.jsxs(k,{children:[e.jsx(r,{value:"all",children:"All Types"}),e.jsx(r,{value:"announcement",children:"Announcement"}),e.jsx(r,{value:"discussion",children:"Discussion"}),e.jsx(r,{value:"event",children:"Event"}),e.jsx(r,{value:"news",children:"News"})]})]})]})})}),e.jsx("div",{className:"space-y-4",children:z?e.jsx("div",{className:"text-center py-8",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-gray-500",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"}),"Loading posts..."]})}):d.length>0?d.map(s=>e.jsx(v,{className:"hover:shadow-md transition-shadow",children:e.jsx(y,{className:"p-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:s.title}),s.is_pinned&&e.jsx(A,{className:"w-4 h-4 text-blue-600"}),q(s),e.jsx(j,{variant:"outline",className:"capitalize",children:s.type})]}),e.jsx("p",{className:"text-gray-600 mb-3",children:R(s.content)}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[V(s.visibility),e.jsx("span",{className:"capitalize",children:s.visibility.replace("_"," ")})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(K,{className:"w-4 h-4"}),e.jsx("span",{children:s.reactions_count})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Q,{className:"w-4 h-4"}),e.jsx("span",{children:s.comments_count})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"w-4 h-4"}),e.jsx("span",{children:new Date(s.created_at).toLocaleDateString()})]}),s.organization&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(b,{className:"w-4 h-4"}),e.jsx("span",{children:s.organization.name})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[e.jsx(u,{href:`/posts/${s.id}`,children:e.jsx(c,{variant:"ghost",size:"sm",children:e.jsx(X,{className:"w-4 h-4"})})}),e.jsx(u,{href:`/posts/${s.id}/edit`,children:e.jsx(c,{variant:"ghost",size:"sm",children:e.jsx(Y,{className:"w-4 h-4"})})}),e.jsx(c,{variant:"ghost",size:"sm",onClick:()=>M(s.id,s.is_pinned),className:s.is_pinned?"text-blue-600":"",children:e.jsx(A,{className:"w-4 h-4"})}),e.jsx(c,{variant:"ghost",size:"sm",onClick:()=>F(s.id),className:"text-red-600 hover:text-red-700",children:e.jsx(J,{className:"w-4 h-4"})})]})]})})},s.id)):e.jsx(v,{children:e.jsxs(y,{className:"p-8 text-center",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No posts found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"You haven't created any posts yet. Start sharing your thoughts with the community!"}),e.jsx(u,{href:"/posts/create",children:e.jsxs(c,{children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),"Create Your First Post"]})})]})})}),g>1&&e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsx(c,{variant:"outline",onClick:()=>f(o-1),disabled:o===1,children:"Previous"}),e.jsxs("span",{className:"flex items-center px-4 text-sm text-gray-600",children:["Page ",o," of ",g]}),e.jsx(c,{variant:"outline",onClick:()=>f(o+1),disabled:o===g,children:"Next"})]})]})]})}export{ge as default};
