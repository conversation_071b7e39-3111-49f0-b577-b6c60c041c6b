import { useState, useEffect } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    Search,
    Filter,
    BookOpen,
    Users,
    Clock,
    Star,
    Calendar,
    MapPin
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Courses',
        href: '/courses',
    },
];

interface Course {
    id: number;
    title: string;
    description: string;
    code: string;
    credits: number;
    semester: string;
    year: string;
    instructor: {
        id: number;
        name: string;
        avatar?: string;
    };
    department: string;
    campus: string;
    schedule: string;
    room: string;
    enrolled_count: number;
    max_capacity: number;
    status: 'active' | 'inactive' | 'full';
    rating: number;
    is_enrolled: boolean;
    created_at: string;
}

interface CoursesData {
    data: Course[];
    current_page: number;
    last_page: number;
    total: number;
}

export default function CoursesIndex() {
    const [courses, setCourses] = useState<CoursesData | null>(null);
    const [loading, setLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState({
        department: '',
        campus: '',
        semester: '',
        year: '',
        status: 'active',
        enrolled_only: false
    });

    const loadCourses = async (page = 1) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '12',
                ...(searchQuery && { search: searchQuery }),
                ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== '' && v !== false))
            });

            const response = await fetch(`/api/v1/courses?${params}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setCourses(data);
            }
        } catch (error) {
            console.error('Error loading courses:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        loadCourses(1);
    };

    const handleFilterChange = (newFilters: typeof filters) => {
        setFilters(newFilters);
        loadCourses(1);
    };

    const handleEnroll = async (courseId: number) => {
        try {
            const response = await fetch(`/api/v1/courses/${courseId}/enroll`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                // Refresh courses to update enrollment status
                loadCourses(courses?.current_page || 1);
            } else {
                alert('Failed to enroll in course');
            }
        } catch (error) {
            console.error('Error enrolling in course:', error);
            alert('An error occurred while enrolling');
        }
    };

    const getStatusBadge = (course: Course) => {
        if (course.status === 'full') {
            return <span className="badge bg-danger">Full</span>;
        }
        if (course.status === 'inactive') {
            return <span className="badge bg-secondary">Inactive</span>;
        }
        if (course.is_enrolled) {
            return <span className="badge bg-success">Enrolled</span>;
        }
        return <span className="badge bg-primary">Available</span>;
    };

    useEffect(() => {
        loadCourses();
    }, []);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Courses" />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        {/* Header */}
                        <div className="d-flex justify-content-between align-items-center mb-4">
                            <h1 className="h3 mb-0">Courses</h1>
                            <div className="d-flex gap-2">
                                <Link href="/courses/my" className="btn btn-outline-primary">
                                    <BookOpen className="w-4 h-4 me-2" />
                                    My Courses
                                </Link>
                            </div>
                        </div>

                        {/* Search and Filters */}
                        <div className="card shadow-sm mb-4">
                            <div className="card-body">
                                <form onSubmit={handleSearch} className="d-flex gap-2 mb-3">
                                    <div className="flex-grow-1">
                                        <input
                                            type="text"
                                            className="form-control"
                                            placeholder="Search courses by title, code, or instructor..."
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                        />
                                    </div>
                                    <button type="submit" className="btn btn-outline-primary">
                                        <Search className="w-4 h-4" />
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => setShowFilters(!showFilters)}
                                        className="btn btn-outline-secondary"
                                    >
                                        <Filter className="w-4 h-4" />
                                    </button>
                                </form>

                                {showFilters && (
                                    <div className="border-top pt-3">
                                        <div className="row g-3">
                                            <div className="col-md-3">
                                                <select
                                                    className="form-select"
                                                    value={filters.department}
                                                    onChange={(e) => setFilters({...filters, department: e.target.value})}
                                                >
                                                    <option value="">All Departments</option>
                                                    <option value="Computer Science">Computer Science</option>
                                                    <option value="Engineering">Engineering</option>
                                                    <option value="Business">Business</option>
                                                    <option value="Arts">Arts</option>
                                                </select>
                                            </div>
                                            <div className="col-md-3">
                                                <select
                                                    className="form-select"
                                                    value={filters.campus}
                                                    onChange={(e) => setFilters({...filters, campus: e.target.value})}
                                                >
                                                    <option value="">All Campuses</option>
                                                    <option value="Main Campus">Main Campus</option>
                                                    <option value="North Campus">North Campus</option>
                                                    <option value="South Campus">South Campus</option>
                                                </select>
                                            </div>
                                            <div className="col-md-3">
                                                <select
                                                    className="form-select"
                                                    value={filters.semester}
                                                    onChange={(e) => setFilters({...filters, semester: e.target.value})}
                                                >
                                                    <option value="">All Semesters</option>
                                                    <option value="Fall">Fall</option>
                                                    <option value="Spring">Spring</option>
                                                    <option value="Summer">Summer</option>
                                                </select>
                                            </div>
                                            <div className="col-md-3">
                                                <div className="form-check">
                                                    <input
                                                        type="checkbox"
                                                        id="enrolled_only"
                                                        className="form-check-input"
                                                        checked={filters.enrolled_only}
                                                        onChange={(e) => setFilters({...filters, enrolled_only: e.target.checked})}
                                                    />
                                                    <label htmlFor="enrolled_only" className="form-check-label">
                                                        My Courses Only
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Courses Grid */}
                        {loading ? (
                            <div className="text-center py-5">
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <p className="mt-3 text-muted">Loading courses...</p>
                            </div>
                        ) : courses && courses.data.length > 0 ? (
                            <>
                                <div className="row g-4">
                                    {courses.data.map((course) => (
                                        <div key={course.id} className="col-md-6 col-lg-4">
                                            <div className="card h-100 shadow-sm">
                                                <div className="card-body">
                                                    <div className="d-flex justify-content-between align-items-start mb-2">
                                                        <h5 className="card-title mb-0">
                                                            <Link 
                                                                href={`/courses/${course.id}`}
                                                                className="text-decoration-none"
                                                            >
                                                                {course.title}
                                                            </Link>
                                                        </h5>
                                                        {getStatusBadge(course)}
                                                    </div>
                                                    
                                                    <p className="text-muted small mb-2">{course.code} • {course.credits} Credits</p>
                                                    
                                                    <p className="card-text small text-muted mb-3">
                                                        {course.description.length > 100 
                                                            ? course.description.substring(0, 100) + '...'
                                                            : course.description}
                                                    </p>

                                                    <div className="small text-muted mb-3">
                                                        <div className="d-flex align-items-center mb-1">
                                                            <Users className="w-3 h-3 me-1" />
                                                            <span>{course.instructor.name}</span>
                                                        </div>
                                                        <div className="d-flex align-items-center mb-1">
                                                            <Calendar className="w-3 h-3 me-1" />
                                                            <span>{course.semester} {course.year}</span>
                                                        </div>
                                                        <div className="d-flex align-items-center mb-1">
                                                            <Clock className="w-3 h-3 me-1" />
                                                            <span>{course.schedule}</span>
                                                        </div>
                                                        <div className="d-flex align-items-center">
                                                            <MapPin className="w-3 h-3 me-1" />
                                                            <span>{course.room} • {course.campus}</span>
                                                        </div>
                                                    </div>

                                                    <div className="d-flex justify-content-between align-items-center">
                                                        <div className="d-flex align-items-center">
                                                            <Star className="w-4 h-4 text-warning me-1" />
                                                            <span className="small">{course.rating.toFixed(1)}</span>
                                                        </div>
                                                        <small className="text-muted">
                                                            {course.enrolled_count}/{course.max_capacity} enrolled
                                                        </small>
                                                    </div>
                                                </div>
                                                
                                                <div className="card-footer bg-light">
                                                    {course.is_enrolled ? (
                                                        <Link 
                                                            href={`/courses/${course.id}`}
                                                            className="btn btn-outline-primary w-100"
                                                        >
                                                            View Course
                                                        </Link>
                                                    ) : course.status === 'full' ? (
                                                        <button className="btn btn-secondary w-100" disabled>
                                                            Course Full
                                                        </button>
                                                    ) : course.status === 'inactive' ? (
                                                        <button className="btn btn-secondary w-100" disabled>
                                                            Not Available
                                                        </button>
                                                    ) : (
                                                        <button 
                                                            onClick={() => handleEnroll(course.id)}
                                                            className="btn btn-primary w-100"
                                                        >
                                                            Enroll Now
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* Pagination */}
                                {courses.last_page > 1 && (
                                    <nav className="mt-4">
                                        <div className="d-flex justify-content-center">
                                            <div className="btn-group">
                                                {Array.from({ length: courses.last_page }, (_, i) => i + 1).map(page => (
                                                    <button
                                                        key={page}
                                                        onClick={() => loadCourses(page)}
                                                        className={`btn ${page === courses.current_page ? 'btn-primary' : 'btn-outline-primary'}`}
                                                    >
                                                        {page}
                                                    </button>
                                                ))}
                                            </div>
                                        </div>
                                    </nav>
                                )}
                            </>
                        ) : (
                            <div className="card shadow-sm">
                                <div className="card-body text-center py-5">
                                    <BookOpen className="w-12 h-12 text-muted mx-auto mb-3" />
                                    <h3 className="h5 mb-2">No courses found</h3>
                                    <p className="text-muted mb-4">
                                        {searchQuery || Object.values(filters).some(v => v) 
                                            ? 'Try adjusting your search or filters.' 
                                            : 'No courses are currently available.'}
                                    </p>
                                    {(searchQuery || Object.values(filters).some(v => v)) && (
                                        <button
                                            onClick={() => {
                                                setSearchQuery('');
                                                setFilters({
                                                    department: '',
                                                    campus: '',
                                                    semester: '',
                                                    year: '',
                                                    status: 'active',
                                                    enrolled_only: false
                                                });
                                                loadCourses(1);
                                            }}
                                            className="btn btn-outline-primary"
                                        >
                                            Clear Filters
                                        </button>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
