import { useState, useEffect, useCallback } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import FeedFilters from '@/components/feed-filters';
import CreatePostModal from '@/components/create-post-modal';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    Plus,
    Search,
    Filter,
    TrendingUp,
    RefreshCw,
    Users,
    FileText,
    Calendar,
    Settings
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Feed',
        href: '/feed',
    },
];

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
}

interface FeedProps {
    initialPosts?: {
        data: Post[];
        current_page: number;
        last_page: number;
        total: number;
    };
    feedType?: 'personalized' | 'trending' | 'all';
}

function Feed({ initialPosts, feedType = 'personalized' }: FeedProps) {
    const { props } = usePage();
    const apiToken = (props.auth as any)?.api_token;

    const [posts, setPosts] = useState<Post[]>(initialPosts?.data || []);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [hasMore, setHasMore] = useState(initialPosts ? initialPosts.current_page < initialPosts.last_page : true);
    const [currentPage, setCurrentPage] = useState(initialPosts?.current_page || 1);
    const [searchQuery, setSearchQuery] = useState('');
    const [showFilters, setShowFilters] = useState(false);
    const [showCreatePost, setShowCreatePost] = useState(false);
    const [activeTab, setActiveTab] = useState<'personalized' | 'trending' | 'all'>(feedType);

    // Debug: Log the props to see if data is being received
    console.log('Feed component props:', { initialPosts, feedType, posts });
    
    const [filters, setFilters] = useState({
        type: '',
        organization_id: '',
        campus: '',
        date_from: '',
        date_to: '',
        sort_by: 'created_at',
        sort_order: 'desc'
    });

    // Load posts based on active tab and filters
    const loadPosts = useCallback(async (page = 1, append = false) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '15',
                ...(searchQuery && { search: searchQuery }),
                ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== ''))
            });

            let endpoint = '/api/v1/posts';
            if (activeTab === 'personalized') {
                endpoint = '/api/v1/feed';
            } else if (activeTab === 'trending') {
                endpoint = '/api/v1/feed/trending';
            }

            const response = await fetch(`${endpoint}?${params}`, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (append) {
                    setPosts(prev => [...prev, ...data.data]);
                } else {
                    setPosts(data.data);
                }
                setCurrentPage(data.current_page);
                setHasMore(data.current_page < data.last_page);
            }
        } catch (error) {
            console.error('Error loading posts:', error);
        } finally {
            setLoading(false);
        }
    }, [activeTab, searchQuery, filters]);

    // Load more posts for infinite scroll
    const loadMore = useCallback(() => {
        if (!loading && hasMore) {
            loadPosts(currentPage + 1, true);
        }
    }, [loading, hasMore, currentPage, loadPosts]);

    // Refresh feed
    const refreshFeed = useCallback(async () => {
        setRefreshing(true);
        await loadPosts(1, false);
        setRefreshing(false);
    }, [loadPosts]);

    // Handle search
    const handleSearch = useCallback((e: React.FormEvent) => {
        e.preventDefault();
        loadPosts(1, false);
    }, [loadPosts]);

    // Handle filter changes
    const handleFilterChange = useCallback((newFilters: typeof filters) => {
        setFilters(newFilters);
        loadPosts(1, false);
    }, [loadPosts]);

    // Handle tab change
    const handleTabChange = useCallback((tab: 'personalized' | 'trending' | 'all') => {
        setActiveTab(tab);
        setCurrentPage(1);
        setPosts([]);
    }, []);

    // Load posts when tab changes
    useEffect(() => {
        loadPosts(1, false);
    }, [activeTab]);

    // Infinite scroll effect
    useEffect(() => {
        const handleScroll = () => {
            if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight || loading) {
                return;
            }
            loadMore();
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [loadMore, loading]);

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiToken}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { 
                                ...post, 
                                reactions_count: post.user_reaction === reactionType 
                                    ? post.reactions_count - 1 
                                    : post.reactions_count + (post.user_reaction ? 0 : 1),
                                user_reaction: post.user_reaction === reactionType ? undefined : reactionType
                            }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        // Implement share functionality
        navigator.share?.({
            title: 'UniLink Post',
            url: `${window.location.origin}/posts/${postId}`
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Feed" />

            <div className="main-container">
                <div className="content-area">
                    <div className="container">
                        <div className="feed-layout-wrapper">
                            <div className="row g-3">
                                {/* Left Sidebar */}
                                <div className="col-lg-3 col-md-3 d-none d-md-block">
                                    <div className="sidebar-content">
                                        <div className="card shadow-sm">
                                            <div className="card-header bg-unilink-primary text-white">
                                                <h5 className="mb-0">Menu</h5>
                                            </div>
                                            <div className="list-group list-group-flush">
                                                <Link href="/profile" className="list-group-item list-group-item-action">
                                                    <Users className="w-4 h-4 me-2" />
                                                    Profile
                                                </Link>
                                                <Link href="/organizations" className="list-group-item list-group-item-action">
                                                    <Users className="w-4 h-4 me-2" />
                                                    My Organizations
                                                </Link>
                                                <Link href="/events" className="list-group-item list-group-item-action">
                                                    <Calendar className="w-4 h-4 me-2" />
                                                    Events
                                                </Link>
                                                <Link href="/settings" className="list-group-item list-group-item-action">
                                                    <Settings className="w-4 h-4 me-2" />
                                                    Settings
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Main Feed */}
                                <div className="col-lg-6 col-md-9 col-12">
                                {/* Filter Buttons */}
                                <div className="card shadow-sm mb-4">
                                    <div className="card-body">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="d-flex align-items-center">
                                                <button
                                                    onClick={() => handleTabChange('personalized')}
                                                    className={`btn btn-sm me-2 ${activeTab === 'personalized' ? 'btn-primary' : 'btn-outline-primary'}`}
                                                >
                                                    All
                                                </button>

                                                <button
                                                    onClick={() => setShowFilters(!showFilters)}
                                                    className="btn btn-outline-primary btn-sm"
                                                >
                                                    <Filter className="w-4 h-4 me-1" />
                                                    Filter
                                                </button>
                                            </div>

                                            <button
                                                onClick={() => setShowCreatePost(true)}
                                                className="btn btn-primary btn-sm"
                                            >
                                                <Plus className="w-4 h-4 me-1" />
                                                Create Post
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {/* Filters Panel */}
                                {showFilters && (
                                    <FeedFilters
                                        filters={filters}
                                        onFiltersChange={handleFilterChange}
                                        onClose={() => setShowFilters(false)}
                                    />
                                )}

                                {/* Scrollable Feed Content */}
                                <div className="feed-content">
                                    {posts.length > 0 ? (
                                        posts.map((post) => (
                                            <PostCard
                                                key={post.id}
                                                post={post}
                                                onReact={handleReaction}
                                                onComment={handleComment}
                                                onShare={handleShare}
                                            />
                                        ))
                                    ) : !loading ? (
                                        <div className="card shadow-sm mb-4">
                                            <div className="card-body text-center">
                                                <FileText className="w-12 h-12 text-muted mx-auto mb-4" />
                                                <h3 className="h5 mb-2">No posts found</h3>
                                                <p className="text-muted mb-4">
                                                    Be the first to add a post!
                                                </p>
                                                <button
                                                    onClick={() => setShowCreatePost(true)}
                                                    className="btn btn-primary"
                                                >
                                                    <Plus className="w-4 h-4 me-2" />
                                                    Create Post
                                                </button>
                                            </div>
                                        </div>
                                    ) : null}

                                    {/* Loading Skeletons */}
                                    {loading && (
                                        <div>
                                            {[...Array(3)].map((_, i) => (
                                                <div key={i} className="card shadow-sm mb-4">
                                                    <div className="card-body">
                                                        <div className="d-flex align-items-center mb-3">
                                                            <div className="placeholder-glow">
                                                                <div className="placeholder rounded-circle me-3" style={{width: '40px', height: '40px'}}></div>
                                                            </div>
                                                            <div className="placeholder-glow flex-grow-1">
                                                                <div className="placeholder col-4 mb-2"></div>
                                                                <div className="placeholder col-3"></div>
                                                            </div>
                                                        </div>
                                                        <div className="placeholder-glow">
                                                            <div className="placeholder col-8 mb-3"></div>
                                                            <div className="placeholder col-12 mb-3" style={{height: '80px'}}></div>
                                                            <div className="d-flex">
                                                                <div className="placeholder col-2 me-2"></div>
                                                                <div className="placeholder col-2 me-2"></div>
                                                                <div className="placeholder col-2"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>

                                {/* Right Sidebar */}
                                <div className="col-lg-3 d-none d-lg-block">
                                    <div className="sidebar-content">
                                        <div className="card shadow-sm mb-4">
                                            <div className="card-header bg-unilink-primary text-white">
                                                <h5 className="mb-0">Upcoming Events</h5>
                                            </div>
                                            <div className="card-body">
                                                <div className="small text-muted mb-2">
                                                    Enrollment Period: Aug 1-15
                                                </div>
                                                <div className="small text-muted mb-2">
                                                    Orientation Day: Aug 20
                                                </div>
                                                <div className="small text-muted mb-2">
                                                    First Day of Classes: Aug 22
                                                </div>
                                                <div className="small text-muted">
                                                    Foundation Day: Sept 15
                                                </div>
                                            </div>
                                        </div>

                                        <div className="card shadow-sm">
                                            <div className="card-header bg-unilink-primary text-white">
                                                <h5 className="mb-0">Quick Links</h5>
                                            </div>
                                            <div className="list-group list-group-flush">
                                                <a href="https://sksu.edu.ph/" target="_blank" rel="noopener noreferrer" className="list-group-item list-group-item-action">
                                                    University Website
                                                </a>
                                                <a href="#" className="list-group-item list-group-item-action">
                                                    Student Portal
                                                </a>
                                                <a href="#" className="list-group-item list-group-item-action">
                                                    Library Resources
                                                </a>
                                                <a href="#" className="list-group-item list-group-item-action">
                                                    Academic Calendar
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Create Post Modal */}
            {showCreatePost && (
                <CreatePostModal
                    onClose={() => setShowCreatePost(false)}
                    onPostCreated={(newPost) => {
                        setPosts(prev => [newPost, ...prev]);
                        setShowCreatePost(false);
                    }}
                />
            )}
        </AppLayout>
    );
}

export default Feed;
