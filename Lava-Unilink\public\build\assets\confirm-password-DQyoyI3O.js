import{m as d,j as s,L as m}from"./app-BYdmc_gi.js";import{I as l}from"./input-error-C0-YTGEW.js";import{B as c}from"./app-logo-icon-D4PatI0y.js";import{I as u}from"./input-BSXvSJcB.js";import{L as f}from"./label-Dipyvm_h.js";import{A as w,L as h}from"./auth-layout-Dhv6Fu2V.js";import"./index-CGyr8X69.js";function y(){const{data:o,setData:e,post:t,processing:a,errors:i,reset:n}=d({password:""}),p=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>n("password")})};return s.jsxs(w,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(m,{title:"Confirm password"}),s.jsx("form",{onSubmit:p,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(f,{htmlFor:"password",children:"Password"}),s.jsx(u,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:o.password,autoFocus:!0,onChange:r=>e("password",r.target.value)}),s.jsx(l,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(c,{className:"w-full",disabled:a,children:[a&&s.jsx(h,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{y as default};
