import { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import {
    ArrowLeft,
    Upload,
    X,
    AlertCircle,
    Eye,
    Users,
    Lock
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Posts',
        href: '/posts',
    },
    {
        title: 'Create Post',
        href: '/posts/create',
    },
];

interface Organization {
    id: number;
    name: string;
    type: string;
}

export default function CreatePost() {
    const [formData, setFormData] = useState({
        title: '',
        content: '',
        type: 'discussion',
        visibility: 'public',
        organization_id: '',
        comments_enabled: true,
        is_pinned: false,
        media: [] as File[]
    });
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [preview, setPreview] = useState(false);

    // Load user's organizations
    useEffect(() => {
        const fetchOrganizations = async () => {
            try {
                const response = await fetch('/api/v1/organizations/my', {
                    headers: {
                        'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                        'Accept': 'application/json',
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    setOrganizations(data.data || []);
                }
            } catch (error) {
                console.error('Error loading organizations:', error);
            }
        };

        fetchOrganizations();
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setFormData(prev => ({
            ...prev,
            media: [...prev.media, ...files].slice(0, 5) // Limit to 5 files
        }));
    };

    const removeFile = (index: number) => {
        setFormData(prev => ({
            ...prev,
            media: prev.media.filter((_, i) => i !== index)
        }));
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.title.trim()) {
            newErrors.title = 'Title is required';
        } else if (formData.title.length > 255) {
            newErrors.title = 'Title must be less than 255 characters';
        }

        if (!formData.content.trim()) {
            newErrors.content = 'Content is required';
        } else if (formData.content.length > 10000) {
            newErrors.content = 'Content must be less than 10,000 characters';
        }

        if (formData.visibility === 'members_only' && !formData.organization_id) {
            newErrors.organization_id = 'Organization is required for members-only posts';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const submitData = new FormData();
            Object.entries(formData).forEach(([key, value]) => {
                if (key === 'media') {
                    (value as File[]).forEach((file, index) => {
                        submitData.append(`media[${index}]`, file);
                    });
                } else {
                    submitData.append(key, value.toString());
                }
            });

            const response = await fetch('/api/v1/posts', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: submitData,
            });

            if (response.ok) {
                const data = await response.json();
                router.visit(`/posts/${data.id}`, {
                    onSuccess: () => {
                        // Show success message
                    }
                });
            } else {
                const errorData = await response.json();
                setErrors(errorData.errors || { general: 'Failed to create post' });
            }
        } catch (error) {
            console.error('Error creating post:', error);
            setErrors({ general: 'An unexpected error occurred' });
        } finally {
            setLoading(false);
        }
    };

    const getVisibilityIcon = (visibility: string) => {
        switch (visibility) {
            case 'public': return <Eye className="w-4 h-4" />;
            case 'members_only': return <Users className="w-4 h-4" />;
            case 'private': return <Lock className="w-4 h-4" />;
            default: return <Eye className="w-4 h-4" />;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Post" />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        <div className="row">
                            <div className="col-md-8 mx-auto">
                                {/* Header */}
                                <div className="d-flex align-items-center mb-4">
                                    <button
                                        onClick={() => router.visit('/posts')}
                                        className="btn btn-outline-secondary me-3"
                                    >
                                        <ArrowLeft className="w-4 h-4" />
                                    </button>
                                    <h1 className="h3 mb-0">Create New Post</h1>
                                </div>

                                {/* Form */}
                                <form onSubmit={handleSubmit}>
                                    <div className="card shadow-sm">
                                        <div className="card-body">
                                            {/* General Error */}
                                            {errors.general && (
                                                <div className="alert alert-danger d-flex align-items-center mb-4">
                                                    <AlertCircle className="w-4 h-4 me-2" />
                                                    {errors.general}
                                                </div>
                                            )}

                                            {/* Title */}
                                            <div className="mb-3">
                                                <label htmlFor="title" className="form-label">
                                                    Title <span className="text-danger">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    id="title"
                                                    name="title"
                                                    className={`form-control ${errors.title ? 'is-invalid' : ''}`}
                                                    value={formData.title}
                                                    onChange={handleInputChange}
                                                    placeholder="Enter post title..."
                                                    maxLength={255}
                                                />
                                                {errors.title && (
                                                    <div className="invalid-feedback">{errors.title}</div>
                                                )}
                                            </div>

                                            {/* Content */}
                                            <div className="mb-3">
                                                <label htmlFor="content" className="form-label">
                                                    Content <span className="text-danger">*</span>
                                                </label>
                                                <textarea
                                                    id="content"
                                                    name="content"
                                                    className={`form-control ${errors.content ? 'is-invalid' : ''}`}
                                                    rows={8}
                                                    value={formData.content}
                                                    onChange={handleInputChange}
                                                    placeholder="Write your post content..."
                                                    maxLength={10000}
                                                />
                                                {errors.content && (
                                                    <div className="invalid-feedback">{errors.content}</div>
                                                )}
                                                <div className="form-text">
                                                    {formData.content.length}/10,000 characters
                                                </div>
                                            </div>

                                            {/* Type and Visibility Row */}
                                            <div className="row mb-3">
                                                <div className="col-md-6">
                                                    <label htmlFor="type" className="form-label">Post Type</label>
                                                    <select
                                                        id="type"
                                                        name="type"
                                                        className="form-select"
                                                        value={formData.type}
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="discussion">Discussion</option>
                                                        <option value="announcement">Announcement</option>
                                                        <option value="event">Event</option>
                                                        <option value="news">News</option>
                                                    </select>
                                                </div>
                                                <div className="col-md-6">
                                                    <label htmlFor="visibility" className="form-label">Visibility</label>
                                                    <select
                                                        id="visibility"
                                                        name="visibility"
                                                        className="form-select"
                                                        value={formData.visibility}
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="public">Public</option>
                                                        <option value="members_only">Members Only</option>
                                                        <option value="private">Private</option>
                                                    </select>
                                                </div>
                                            </div>

                                            {/* Organization (if members only) */}
                                            {formData.visibility === 'members_only' && (
                                                <div className="mb-3">
                                                    <label htmlFor="organization_id" className="form-label">
                                                        Organization <span className="text-danger">*</span>
                                                    </label>
                                                    <select
                                                        id="organization_id"
                                                        name="organization_id"
                                                        className={`form-select ${errors.organization_id ? 'is-invalid' : ''}`}
                                                        value={formData.organization_id}
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="">Select an organization</option>
                                                        {organizations.map(org => (
                                                            <option key={org.id} value={org.id}>
                                                                {org.name}
                                                            </option>
                                                        ))}
                                                    </select>
                                                    {errors.organization_id && (
                                                        <div className="invalid-feedback">{errors.organization_id}</div>
                                                    )}
                                                </div>
                                            )}

                                            {/* Media Upload */}
                                            <div className="mb-3">
                                                <label className="form-label">Media (Optional)</label>
                                                <div className="border rounded p-3">
                                                    <input
                                                        type="file"
                                                        multiple
                                                        accept="image/*,video/*,.pdf,.doc,.docx"
                                                        onChange={handleFileChange}
                                                        className="form-control mb-2"
                                                        disabled={formData.media.length >= 5}
                                                    />
                                                    <small className="text-muted">
                                                        Upload up to 5 files (images, videos, documents)
                                                    </small>
                                                    
                                                    {/* File Preview */}
                                                    {formData.media.length > 0 && (
                                                        <div className="mt-3">
                                                            {formData.media.map((file, index) => (
                                                                <div key={index} className="d-flex align-items-center justify-content-between bg-light p-2 rounded mb-2">
                                                                    <span className="small">{file.name}</span>
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => removeFile(index)}
                                                                        className="btn btn-sm btn-outline-danger"
                                                                    >
                                                                        <X className="w-3 h-3" />
                                                                    </button>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Options */}
                                            <div className="mb-4">
                                                <div className="form-check">
                                                    <input
                                                        type="checkbox"
                                                        id="comments_enabled"
                                                        name="comments_enabled"
                                                        className="form-check-input"
                                                        checked={formData.comments_enabled}
                                                        onChange={handleInputChange}
                                                    />
                                                    <label htmlFor="comments_enabled" className="form-check-label">
                                                        Enable comments
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Footer */}
                                        <div className="card-footer bg-light">
                                            <div className="d-flex justify-content-between align-items-center">
                                                <div className="d-flex align-items-center text-muted small">
                                                    {getVisibilityIcon(formData.visibility)}
                                                    <span className="ms-1">
                                                        {formData.visibility === 'public' && 'Everyone can see this post'}
                                                        {formData.visibility === 'members_only' && 'Only organization members can see this post'}
                                                        {formData.visibility === 'private' && 'Only you can see this post'}
                                                    </span>
                                                </div>
                                                <div className="d-flex gap-2">
                                                    <button
                                                        type="button"
                                                        onClick={() => router.visit('/posts')}
                                                        className="btn btn-outline-secondary"
                                                        disabled={loading}
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        type="submit"
                                                        className="btn btn-primary"
                                                        disabled={loading}
                                                    >
                                                        {loading ? (
                                                            <>
                                                                <span className="spinner-border spinner-border-sm me-2" />
                                                                Creating...
                                                            </>
                                                        ) : (
                                                            'Create Post'
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
