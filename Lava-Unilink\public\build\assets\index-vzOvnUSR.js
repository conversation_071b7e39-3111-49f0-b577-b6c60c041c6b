import{r as n,j as e,L as $,$ as g}from"./app-BYRFhzWn.js";import{A as M}from"./app-layout-K8hMRWQO.js";import{P as O}from"./post-card-Ds7XEjKL.js";import{P as y}from"./plus-BbRVJjQf.js";import{S as P}from"./search-Bp5g1-i-.js";import{F as R}from"./filter-CvLLZWlP.js";import{F as z}from"./file-text-DIrwCbac.js";import"./app-logo-icon-BTP1W0LK.js";import"./index-CEpWp31w.js";import"./index-BNlTrGhe.js";import"./pin-CdfMYizT.js";import"./message-circle-Br2KZulK.js";const q=[{title:"Posts",href:"/posts"}];function W({initialPosts:a}){const[p,x]=n.useState((a==null?void 0:a.data)||[]),[m,f]=n.useState(!1),[b,_]=n.useState(a?a.current_page<a.last_page:!0),[w,S]=n.useState((a==null?void 0:a.current_page)||1),[u,v]=n.useState(""),[N,C]=n.useState(!1),[c,j]=n.useState({type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"}),h=async(t=1,r=!1)=>{var i;f(!0);try{const l=new URLSearchParams({page:t.toString(),per_page:"15",...u&&{search:u},...Object.fromEntries(Object.entries(c).filter(([o,s])=>s!==""))}),d=await fetch(`/api/v1/posts?${l}`,{headers:{Authorization:`Bearer ${(i=document.querySelector('meta[name="api-token"]'))==null?void 0:i.getAttribute("content")}`,Accept:"application/json"}});if(d.ok){const o=await d.json();x(r?s=>[...s,...o.data]:o.data),S(o.current_page),_(o.current_page<o.last_page)}}catch(l){console.error("Error loading posts:",l)}finally{f(!1)}},A=()=>{!m&&b&&h(w+1,!0)},k=t=>{t.preventDefault(),h(1,!1)},L=async(t,r)=>{var i,l;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(i=document.querySelector('meta[name="api-token"]'))==null?void 0:i.getAttribute("content")}`,"X-CSRF-TOKEN":((l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:t,type:r})})).ok&&x(o=>o.map(s=>s.id===t?{...s,reactions_count:s.user_reaction===r?s.reactions_count-1:s.reactions_count+(s.user_reaction?0:1),user_reaction:s.user_reaction===r?void 0:r}:s))}catch(d){console.error("Error toggling reaction:",d)}},F=t=>{window.location.href=`/posts/${t}#comments`},E=t=>{var r;(r=navigator.share)==null||r.call(navigator,{title:"UniLink Post",url:`${window.location.origin}/posts/${t}`})};return n.useEffect(()=>{a||h()},[]),e.jsxs(M,{breadcrumbs:q,children:[e.jsx($,{title:"Posts"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-md-8 mx-auto",children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[e.jsx("h1",{className:"h3 mb-0",children:"All Posts"}),e.jsxs(g,{href:"/posts/create",className:"btn btn-primary",children:[e.jsx(y,{className:"w-4 h-4 me-2"}),"Create Post"]})]}),e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("form",{onSubmit:k,className:"d-flex gap-2 mb-3",children:[e.jsx("div",{className:"flex-grow-1",children:e.jsx("input",{type:"text",className:"form-control",placeholder:"Search posts...",value:u,onChange:t=>v(t.target.value)})}),e.jsx("button",{type:"submit",className:"btn btn-outline-primary",children:e.jsx(P,{className:"w-4 h-4"})}),e.jsx("button",{type:"button",onClick:()=>C(!N),className:"btn btn-outline-secondary",children:e.jsx(R,{className:"w-4 h-4"})})]}),N&&e.jsx("div",{className:"border-top pt-3",children:e.jsxs("div",{className:"row g-3",children:[e.jsx("div",{className:"col-md-4",children:e.jsxs("select",{className:"form-select",value:c.type,onChange:t=>j({...c,type:t.target.value}),children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"announcement",children:"Announcement"}),e.jsx("option",{value:"discussion",children:"Discussion"}),e.jsx("option",{value:"event",children:"Event"}),e.jsx("option",{value:"news",children:"News"})]})}),e.jsx("div",{className:"col-md-4",children:e.jsxs("select",{className:"form-select",value:c.sort_by,onChange:t=>j({...c,sort_by:t.target.value}),children:[e.jsx("option",{value:"created_at",children:"Latest"}),e.jsx("option",{value:"reactions_count",children:"Most Liked"}),e.jsx("option",{value:"comments_count",children:"Most Commented"})]})}),e.jsx("div",{className:"col-md-4",children:e.jsx("button",{type:"button",onClick:()=>{j({type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"}),v(""),h(1,!1)},className:"btn btn-outline-secondary w-100",children:"Clear Filters"})})]})})]})}),e.jsxs("div",{className:"posts-list",children:[p.length>0?p.map(t=>e.jsx(O,{post:t,onReact:L,onComment:F,onShare:E},t.id)):m?null:e.jsx("div",{className:"card shadow-sm",children:e.jsxs("div",{className:"card-body text-center py-5",children:[e.jsx(z,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h3",{className:"h5 mb-2",children:"No posts found"}),e.jsx("p",{className:"text-muted mb-4",children:u||Object.values(c).some(t=>t)?"Try adjusting your search or filters.":"Be the first to create a post!"}),e.jsxs(g,{href:"/posts/create",className:"btn btn-primary",children:[e.jsx(y,{className:"w-4 h-4 me-2"}),"Create Post"]})]})}),m&&e.jsx("div",{className:"text-center py-4",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})}),b&&!m&&p.length>0&&e.jsx("div",{className:"text-center mt-4",children:e.jsx("button",{onClick:A,className:"btn btn-outline-primary",children:"Load More Posts"})})]})]})})})})})]})}export{W as default};
