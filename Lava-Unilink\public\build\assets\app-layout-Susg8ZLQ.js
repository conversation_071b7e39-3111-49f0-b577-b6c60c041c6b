import{r as l,t as Tr,j as i,K as Dr,$ as un}from"./app-2kjRWvs5.js";import{a as M,u as Z,S as we,c as N,d as Mr,B as de,b as fn,A as _r}from"./app-logo-icon-ylztpkIq.js";import{d as <PERSON>,e as he,a as B,u as hn,c as mt,P as Ee,f as Lr,b as Ir}from"./index-8HzOgwF5.js";import{P as W,d as Fr,R as Br,r as Wr}from"./index-CdTsfyPs.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $r=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],pn=M("Bell",$r);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hr=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],zr=M("Calendar",Hr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ur=M("ChevronRight",Vr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],qr=M("CircleAlert",Yr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kr=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Gr=M("CircleCheckBig",Kr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Zr=M("Clock",Xr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],Ht=M("Download",Qr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jr=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],eo=M("House",Jr);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const to=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],no=M("MessageSquare",to);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ro=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],oo=M("PanelLeft",ro);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const so=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],ao=M("RefreshCw",so);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const io=[["path",{d:"M4 11a9 9 0 0 1 9 9",key:"pv89mb"}],["path",{d:"M4 4a16 16 0 0 1 16 16",key:"k0647b"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]],co=M("Rss",io);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lo=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],uo=M("Settings",lo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fo=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],ho=M("Smartphone",fo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const po=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]],mo=M("Users",po);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const go=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],zt=M("WifiOff",go);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vo=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]],st=M("Wifi",vo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],gt=M("X",xo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yo=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],bo=M("Zap",yo),Ge=768;function wo(){const[e,t]=l.useState();return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Ge-1}px)`),r=()=>{t(window.innerWidth<Ge)};return n.addEventListener("change",r),t(window.innerWidth<Ge),()=>n.removeEventListener("change",r)},[]),!!e}var Eo=Tr.useId||(()=>{}),Co=0;function Te(e){const[t,n]=l.useState(Eo());return Le(()=>{n(r=>r??String(Co++))},[e]),e||(t?`radix-${t}`:"")}function So(e,t=globalThis==null?void 0:globalThis.document){const n=he(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ao="DismissableLayer",at="dismissableLayer.update",No="dismissableLayer.pointerDownOutside",jo="dismissableLayer.focusOutside",Vt,mn=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),vt=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...d}=e,u=l.useContext(mn),[f,p]=l.useState(null),g=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=l.useState({}),x=Z(t,S=>p(S)),h=Array.from(u.layers),[v]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(v),b=f?h.indexOf(f):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,w=b>=y,C=ko(S=>{const R=S.target,O=[...u.branches].some(P=>P.contains(R));!w||O||(o==null||o(S),a==null||a(S),S.defaultPrevented||c==null||c())},g),A=Oo(S=>{const R=S.target;[...u.branches].some(P=>P.contains(R))||(s==null||s(S),a==null||a(S),S.defaultPrevented||c==null||c())},g);return So(S=>{b===u.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},g),l.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Vt=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),Ut(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=Vt)}},[f,g,n,u]),l.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),Ut())},[f,u]),l.useEffect(()=>{const S=()=>m({});return document.addEventListener(at,S),()=>document.removeEventListener(at,S)},[]),i.jsx(W.div,{...d,ref:x,style:{pointerEvents:E?w?"auto":"none":void 0,...e.style},onFocusCapture:B(e.onFocusCapture,A.onFocusCapture),onBlurCapture:B(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:B(e.onPointerDownCapture,C.onPointerDownCapture)})});vt.displayName=Ao;var Ro="DismissableLayerBranch",Po=l.forwardRef((e,t)=>{const n=l.useContext(mn),r=l.useRef(null),o=Z(t,r);return l.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),i.jsx(W.div,{...e,ref:o})});Po.displayName=Ro;function ko(e,t=globalThis==null?void 0:globalThis.document){const n=he(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let d=function(){gn(No,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=d,t.addEventListener("click",o.current,{once:!0})):d()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Oo(e,t=globalThis==null?void 0:globalThis.document){const n=he(e),r=l.useRef(!1);return l.useEffect(()=>{const o=s=>{s.target&&!r.current&&gn(jo,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Ut(){const e=new CustomEvent(at);document.dispatchEvent(e)}function gn(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Fr(o,s):o.dispatchEvent(s)}var Xe="focusScope.autoFocusOnMount",Ze="focusScope.autoFocusOnUnmount",Yt={bubbles:!1,cancelable:!0},To="FocusScope",vn=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...a}=e,[c,d]=l.useState(null),u=he(o),f=he(s),p=l.useRef(null),g=Z(t,h=>d(h)),m=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let h=function(E){if(m.paused||!c)return;const w=E.target;c.contains(w)?p.current=w:te(p.current,{select:!0})},v=function(E){if(m.paused||!c)return;const w=E.relatedTarget;w!==null&&(c.contains(w)||te(p.current,{select:!0}))},y=function(E){if(document.activeElement===document.body)for(const C of E)C.removedNodes.length>0&&te(c)};document.addEventListener("focusin",h),document.addEventListener("focusout",v);const b=new MutationObserver(y);return c&&b.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",v),b.disconnect()}}},[r,c,m.paused]),l.useEffect(()=>{if(c){Kt.add(m);const h=document.activeElement;if(!c.contains(h)){const y=new CustomEvent(Xe,Yt);c.addEventListener(Xe,u),c.dispatchEvent(y),y.defaultPrevented||(Do(Fo(xn(c)),{select:!0}),document.activeElement===h&&te(c))}return()=>{c.removeEventListener(Xe,u),setTimeout(()=>{const y=new CustomEvent(Ze,Yt);c.addEventListener(Ze,f),c.dispatchEvent(y),y.defaultPrevented||te(h??document.body,{select:!0}),c.removeEventListener(Ze,f),Kt.remove(m)},0)}}},[c,u,f,m]);const x=l.useCallback(h=>{if(!n&&!r||m.paused)return;const v=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,y=document.activeElement;if(v&&y){const b=h.currentTarget,[E,w]=Mo(b);E&&w?!h.shiftKey&&y===w?(h.preventDefault(),n&&te(E,{select:!0})):h.shiftKey&&y===E&&(h.preventDefault(),n&&te(w,{select:!0})):y===b&&h.preventDefault()}},[n,r,m.paused]);return i.jsx(W.div,{tabIndex:-1,...a,ref:g,onKeyDown:x})});vn.displayName=To;function Do(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(te(r,{select:t}),document.activeElement!==n)return}function Mo(e){const t=xn(e),n=qt(t,e),r=qt(t.reverse(),e);return[n,r]}function xn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function qt(e,t){for(const n of e)if(!_o(n,{upTo:t}))return n}function _o(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Lo(e){return e instanceof HTMLInputElement&&"select"in e}function te(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Lo(e)&&t&&e.select()}}var Kt=Io();function Io(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Gt(e,t),e.unshift(t)},remove(t){var n;e=Gt(e,t),(n=e[0])==null||n.resume()}}}function Gt(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Fo(e){return e.filter(t=>t.tagName!=="A")}var Bo="Portal",xt=l.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,s]=l.useState(!1);Le(()=>s(!0),[]);const a=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return a?Br.createPortal(i.jsx(W.div,{...r,ref:t}),a):null});xt.displayName=Bo;var Qe=0;function Wo(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Xt()),document.body.insertAdjacentElement("beforeend",e[1]??Xt()),Qe++,()=>{Qe===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Qe--}},[])}function Xt(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var K=function(){return K=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},K.apply(this,arguments)};function yn(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function $o(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var De="right-scroll-bar-position",Me="width-before-scroll-bar",Ho="with-scroll-bars-hidden",zo="--removed-body-scroll-bar-size";function Je(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Vo(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Uo=typeof window<"u"?l.useLayoutEffect:l.useEffect,Zt=new WeakMap;function Yo(e,t){var n=Vo(null,function(r){return e.forEach(function(o){return Je(o,r)})});return Uo(function(){var r=Zt.get(n);if(r){var o=new Set(r),s=new Set(e),a=n.current;o.forEach(function(c){s.has(c)||Je(c,null)}),s.forEach(function(c){o.has(c)||Je(c,a)})}Zt.set(n,e)},[e]),n}function qo(e){return e}function Ko(e,t){t===void 0&&(t=qo);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var a=t(s,r);return n.push(a),function(){n=n.filter(function(c){return c!==a})}},assignSyncMedium:function(s){for(r=!0;n.length;){var a=n;n=[],a.forEach(s)}n={push:function(c){return s(c)},filter:function(){return n}}},assignMedium:function(s){r=!0;var a=[];if(n.length){var c=n;n=[],c.forEach(s),a=n}var d=function(){var f=a;a=[],f.forEach(s)},u=function(){return Promise.resolve().then(d)};u(),n={push:function(f){a.push(f),u()},filter:function(f){return a=a.filter(f),n}}}};return o}function Go(e){e===void 0&&(e={});var t=Ko(null);return t.options=K({async:!0,ssr:!1},e),t}var bn=function(e){var t=e.sideCar,n=yn(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,K({},n))};bn.isSideCarExport=!0;function Xo(e,t){return e.useMedium(t),bn}var wn=Go(),et=function(){},He=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:et,onWheelCapture:et,onTouchMoveCapture:et}),o=r[0],s=r[1],a=e.forwardProps,c=e.children,d=e.className,u=e.removeScrollBar,f=e.enabled,p=e.shards,g=e.sideCar,m=e.noIsolation,x=e.inert,h=e.allowPinchZoom,v=e.as,y=v===void 0?"div":v,b=e.gapMode,E=yn(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),w=g,C=Yo([n,t]),A=K(K({},E),o);return l.createElement(l.Fragment,null,f&&l.createElement(w,{sideCar:wn,removeScrollBar:u,shards:p,noIsolation:m,inert:x,setCallbacks:s,allowPinchZoom:!!h,lockRef:n,gapMode:b}),a?l.cloneElement(l.Children.only(c),K(K({},A),{ref:C})):l.createElement(y,K({},A,{className:d,ref:C}),c))});He.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};He.classNames={fullWidth:Me,zeroRight:De};var Zo=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Qo(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Zo();return t&&e.setAttribute("nonce",t),e}function Jo(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function es(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var ts=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Qo())&&(Jo(t,n),es(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ns=function(){var e=ts();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},En=function(){var e=ns(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},rs={left:0,top:0,right:0,gap:0},tt=function(e){return parseInt(e||"",10)||0},os=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[tt(n),tt(r),tt(o)]},ss=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return rs;var t=os(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},as=En(),ue="data-scroll-locked",is=function(e,t,n,r){var o=e.left,s=e.top,a=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ho,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(ue,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(De,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Me,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(De," .").concat(De,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Me," .").concat(Me,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ue,`] {
    `).concat(zo,": ").concat(c,`px;
  }
`)},Qt=function(){var e=parseInt(document.body.getAttribute(ue)||"0",10);return isFinite(e)?e:0},cs=function(){l.useEffect(function(){return document.body.setAttribute(ue,(Qt()+1).toString()),function(){var e=Qt()-1;e<=0?document.body.removeAttribute(ue):document.body.setAttribute(ue,e.toString())}},[])},ls=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;cs();var s=l.useMemo(function(){return ss(o)},[o]);return l.createElement(as,{styles:is(s,!t,o,n?"":"!important")})},it=!1;if(typeof window<"u")try{var je=Object.defineProperty({},"passive",{get:function(){return it=!0,!0}});window.addEventListener("test",je,je),window.removeEventListener("test",je,je)}catch{it=!1}var ie=it?{passive:!1}:!1,ds=function(e){return e.tagName==="TEXTAREA"},Cn=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ds(e)&&n[t]==="visible")},us=function(e){return Cn(e,"overflowY")},fs=function(e){return Cn(e,"overflowX")},Jt=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Sn(e,r);if(o){var s=An(e,r),a=s[1],c=s[2];if(a>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},hs=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},ps=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Sn=function(e,t){return e==="v"?us(t):fs(t)},An=function(e,t){return e==="v"?hs(t):ps(t)},ms=function(e,t){return e==="h"&&t==="rtl"?-1:1},gs=function(e,t,n,r,o){var s=ms(e,window.getComputedStyle(t).direction),a=s*r,c=n.target,d=t.contains(c),u=!1,f=a>0,p=0,g=0;do{var m=An(e,c),x=m[0],h=m[1],v=m[2],y=h-v-s*x;(x||y)&&Sn(e,c)&&(p+=y,g+=x),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!d&&c!==document.body||d&&(t.contains(c)||t===c));return(f&&Math.abs(p)<1||!f&&Math.abs(g)<1)&&(u=!0),u},Re=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},en=function(e){return[e.deltaX,e.deltaY]},tn=function(e){return e&&"current"in e?e.current:e},vs=function(e,t){return e[0]===t[0]&&e[1]===t[1]},xs=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ys=0,ce=[];function bs(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(ys++)[0],s=l.useState(En)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var h=$o([e.lockRef.current],(e.shards||[]).map(tn),!0).filter(Boolean);return h.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),h.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(h,v){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!a.current.allowPinchZoom;var y=Re(h),b=n.current,E="deltaX"in h?h.deltaX:b[0]-y[0],w="deltaY"in h?h.deltaY:b[1]-y[1],C,A=h.target,S=Math.abs(E)>Math.abs(w)?"h":"v";if("touches"in h&&S==="h"&&A.type==="range")return!1;var R=Jt(S,A);if(!R)return!0;if(R?C=S:(C=S==="v"?"h":"v",R=Jt(S,A)),!R)return!1;if(!r.current&&"changedTouches"in h&&(E||w)&&(r.current=C),!C)return!0;var O=r.current||C;return gs(O,v,h,O==="h"?E:w)},[]),d=l.useCallback(function(h){var v=h;if(!(!ce.length||ce[ce.length-1]!==s)){var y="deltaY"in v?en(v):Re(v),b=t.current.filter(function(C){return C.name===v.type&&(C.target===v.target||v.target===C.shadowParent)&&vs(C.delta,y)})[0];if(b&&b.should){v.cancelable&&v.preventDefault();return}if(!b){var E=(a.current.shards||[]).map(tn).filter(Boolean).filter(function(C){return C.contains(v.target)}),w=E.length>0?c(v,E[0]):!a.current.noIsolation;w&&v.cancelable&&v.preventDefault()}}},[]),u=l.useCallback(function(h,v,y,b){var E={name:h,delta:v,target:y,should:b,shadowParent:ws(y)};t.current.push(E),setTimeout(function(){t.current=t.current.filter(function(w){return w!==E})},1)},[]),f=l.useCallback(function(h){n.current=Re(h),r.current=void 0},[]),p=l.useCallback(function(h){u(h.type,en(h),h.target,c(h,e.lockRef.current))},[]),g=l.useCallback(function(h){u(h.type,Re(h),h.target,c(h,e.lockRef.current))},[]);l.useEffect(function(){return ce.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:g}),document.addEventListener("wheel",d,ie),document.addEventListener("touchmove",d,ie),document.addEventListener("touchstart",f,ie),function(){ce=ce.filter(function(h){return h!==s}),document.removeEventListener("wheel",d,ie),document.removeEventListener("touchmove",d,ie),document.removeEventListener("touchstart",f,ie)}},[]);var m=e.removeScrollBar,x=e.inert;return l.createElement(l.Fragment,null,x?l.createElement(s,{styles:xs(o)}):null,m?l.createElement(ls,{gapMode:e.gapMode}):null)}function ws(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Es=Xo(wn,bs);var Nn=l.forwardRef(function(e,t){return l.createElement(He,K({},e,{ref:t,sideCar:Es}))});Nn.classNames=He.classNames;var Cs=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},le=new WeakMap,Pe=new WeakMap,ke={},nt=0,jn=function(e){return e&&(e.host||jn(e.parentNode))},Ss=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=jn(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},As=function(e,t,n,r){var o=Ss(t,Array.isArray(e)?e:[e]);ke[n]||(ke[n]=new WeakMap);var s=ke[n],a=[],c=new Set,d=new Set(o),u=function(p){!p||c.has(p)||(c.add(p),u(p.parentNode))};o.forEach(u);var f=function(p){!p||d.has(p)||Array.prototype.forEach.call(p.children,function(g){if(c.has(g))f(g);else try{var m=g.getAttribute(r),x=m!==null&&m!=="false",h=(le.get(g)||0)+1,v=(s.get(g)||0)+1;le.set(g,h),s.set(g,v),a.push(g),h===1&&x&&Pe.set(g,!0),v===1&&g.setAttribute(n,"true"),x||g.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",g,y)}})};return f(t),c.clear(),nt++,function(){a.forEach(function(p){var g=le.get(p)-1,m=s.get(p)-1;le.set(p,g),s.set(p,m),g||(Pe.has(p)||p.removeAttribute(r),Pe.delete(p)),m||p.removeAttribute(n)}),nt--,nt||(le=new WeakMap,le=new WeakMap,Pe=new WeakMap,ke={})}},Ns=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Cs(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),As(r,o,n,"aria-hidden")):function(){return null}},yt="Dialog",[Rn,gc]=mt(yt),[js,Y]=Rn(yt),Pn=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:a=!0}=e,c=l.useRef(null),d=l.useRef(null),[u=!1,f]=hn({prop:r,defaultProp:o,onChange:s});return i.jsx(js,{scope:t,triggerRef:c,contentRef:d,contentId:Te(),titleId:Te(),descriptionId:Te(),open:u,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(p=>!p),[f]),modal:a,children:n})};Pn.displayName=yt;var kn="DialogTrigger",On=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Y(kn,n),s=Z(t,o.triggerRef);return i.jsx(W.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Et(o.open),...r,ref:s,onClick:B(e.onClick,o.onOpenToggle)})});On.displayName=kn;var bt="DialogPortal",[Rs,Tn]=Rn(bt,{forceMount:void 0}),Dn=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Y(bt,t);return i.jsx(Rs,{scope:t,forceMount:n,children:l.Children.map(r,a=>i.jsx(Ee,{present:n||s.open,children:i.jsx(xt,{asChild:!0,container:o,children:a})}))})};Dn.displayName=bt;var Ie="DialogOverlay",Mn=l.forwardRef((e,t)=>{const n=Tn(Ie,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Y(Ie,e.__scopeDialog);return s.modal?i.jsx(Ee,{present:r||s.open,children:i.jsx(Ps,{...o,ref:t})}):null});Mn.displayName=Ie;var Ps=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Y(Ie,n);return i.jsx(Nn,{as:we,allowPinchZoom:!0,shards:[o.contentRef],children:i.jsx(W.div,{"data-state":Et(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),se="DialogContent",_n=l.forwardRef((e,t)=>{const n=Tn(se,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Y(se,e.__scopeDialog);return i.jsx(Ee,{present:r||s.open,children:s.modal?i.jsx(ks,{...o,ref:t}):i.jsx(Os,{...o,ref:t})})});_n.displayName=se;var ks=l.forwardRef((e,t)=>{const n=Y(se,e.__scopeDialog),r=l.useRef(null),o=Z(t,n.contentRef,r);return l.useEffect(()=>{const s=r.current;if(s)return Ns(s)},[]),i.jsx(Ln,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:B(e.onCloseAutoFocus,s=>{var a;s.preventDefault(),(a=n.triggerRef.current)==null||a.focus()}),onPointerDownOutside:B(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,c=a.button===0&&a.ctrlKey===!0;(a.button===2||c)&&s.preventDefault()}),onFocusOutside:B(e.onFocusOutside,s=>s.preventDefault())})}),Os=l.forwardRef((e,t)=>{const n=Y(se,e.__scopeDialog),r=l.useRef(!1),o=l.useRef(!1);return i.jsx(Ln,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var a,c;(a=e.onCloseAutoFocus)==null||a.call(e,s),s.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var d,u;(d=e.onInteractOutside)==null||d.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const a=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(a))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Ln=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...a}=e,c=Y(se,n),d=l.useRef(null),u=Z(t,d);return Wo(),i.jsxs(i.Fragment,{children:[i.jsx(vn,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:i.jsx(vt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Et(c.open),...a,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),i.jsxs(i.Fragment,{children:[i.jsx(Ts,{titleId:c.titleId}),i.jsx(Ms,{contentRef:d,descriptionId:c.descriptionId})]})]})}),wt="DialogTitle",In=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Y(wt,n);return i.jsx(W.h2,{id:o.titleId,...r,ref:t})});In.displayName=wt;var Fn="DialogDescription",Bn=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Y(Fn,n);return i.jsx(W.p,{id:o.descriptionId,...r,ref:t})});Bn.displayName=Fn;var Wn="DialogClose",$n=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Y(Wn,n);return i.jsx(W.button,{type:"button",...r,ref:t,onClick:B(e.onClick,()=>o.onOpenChange(!1))})});$n.displayName=Wn;function Et(e){return e?"open":"closed"}var Hn="DialogTitleWarning",[vc,zn]=Lr(Hn,{contentName:se,titleName:wt,docsSlug:"dialog"}),Ts=({titleId:e})=>{const t=zn(Hn),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Ds="DialogDescriptionWarning",Ms=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${zn(Ds).contentName}}.`;return l.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},_s=Pn,xc=On,Ls=Dn,Is=Mn,Fs=_n,Bs=In,Ws=Bn,$s=$n;function Hs({...e}){return i.jsx(_s,{"data-slot":"sheet",...e})}function zs({...e}){return i.jsx(Ls,{"data-slot":"sheet-portal",...e})}function Vs({className:e,...t}){return i.jsx(Is,{"data-slot":"sheet-overlay",className:N("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function Us({className:e,children:t,side:n="right",...r}){return i.jsxs(zs,{children:[i.jsx(Vs,{}),i.jsxs(Fs,{"data-slot":"sheet-content",className:N("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,i.jsxs($s,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[i.jsx(gt,{className:"size-4"}),i.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Ys({className:e,...t}){return i.jsx("div",{"data-slot":"sheet-header",className:N("flex flex-col gap-1.5 p-4",e),...t})}function qs({className:e,...t}){return i.jsx(Bs,{"data-slot":"sheet-title",className:N("text-foreground font-semibold",e),...t})}function Ks({className:e,...t}){return i.jsx(Ws,{"data-slot":"sheet-description",className:N("text-muted-foreground text-sm",e),...t})}const Gs=["top","right","bottom","left"],ne=Math.min,$=Math.max,Fe=Math.round,Oe=Math.floor,G=e=>({x:e,y:e}),Xs={left:"right",right:"left",bottom:"top",top:"bottom"},Zs={start:"end",end:"start"};function ct(e,t,n){return $(e,ne(t,n))}function J(e,t){return typeof e=="function"?e(t):e}function ee(e){return e.split("-")[0]}function ge(e){return e.split("-")[1]}function Ct(e){return e==="x"?"y":"x"}function St(e){return e==="y"?"height":"width"}function re(e){return["top","bottom"].includes(ee(e))?"y":"x"}function At(e){return Ct(re(e))}function Qs(e,t,n){n===void 0&&(n=!1);const r=ge(e),o=At(e),s=St(o);let a=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(a=Be(a)),[a,Be(a)]}function Js(e){const t=Be(e);return[lt(e),t,lt(t)]}function lt(e){return e.replace(/start|end/g,t=>Zs[t])}function ea(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:a;default:return[]}}function ta(e,t,n,r){const o=ge(e);let s=ea(ee(e),n==="start",r);return o&&(s=s.map(a=>a+"-"+o),t&&(s=s.concat(s.map(lt)))),s}function Be(e){return e.replace(/left|right|bottom|top/g,t=>Xs[t])}function na(e){return{top:0,right:0,bottom:0,left:0,...e}}function Vn(e){return typeof e!="number"?na(e):{top:e,right:e,bottom:e,left:e}}function We(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function nn(e,t,n){let{reference:r,floating:o}=e;const s=re(t),a=At(t),c=St(a),d=ee(t),u=s==="y",f=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,g=r[c]/2-o[c]/2;let m;switch(d){case"top":m={x:f,y:r.y-o.height};break;case"bottom":m={x:f,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:p};break;case"left":m={x:r.x-o.width,y:p};break;default:m={x:r.x,y:r.y}}switch(ge(t)){case"start":m[a]-=g*(n&&u?-1:1);break;case"end":m[a]+=g*(n&&u?-1:1);break}return m}const ra=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),d=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:p}=nn(u,r,d),g=r,m={},x=0;for(let h=0;h<c.length;h++){const{name:v,fn:y}=c[h],{x:b,y:E,data:w,reset:C}=await y({x:f,y:p,initialPlacement:r,placement:g,strategy:o,middlewareData:m,rects:u,platform:a,elements:{reference:e,floating:t}});f=b??f,p=E??p,m={...m,[v]:{...m[v],...w}},C&&x<=50&&(x++,typeof C=="object"&&(C.placement&&(g=C.placement),C.rects&&(u=C.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:f,y:p}=nn(u,g,d)),h=-1)}return{x:f,y:p,placement:g,strategy:o,middlewareData:m}};async function ye(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:a,elements:c,strategy:d}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:g=!1,padding:m=0}=J(t,e),x=Vn(m),v=c[g?p==="floating"?"reference":"floating":p],y=We(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(v)))==null||n?v:v.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:f,strategy:d})),b=p==="floating"?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,E=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),w=await(s.isElement==null?void 0:s.isElement(E))?await(s.getScale==null?void 0:s.getScale(E))||{x:1,y:1}:{x:1,y:1},C=We(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:E,strategy:d}):b);return{top:(y.top-C.top+x.top)/w.y,bottom:(C.bottom-y.bottom+x.bottom)/w.y,left:(y.left-C.left+x.left)/w.x,right:(C.right-y.right+x.right)/w.x}}const oa=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:a,elements:c,middlewareData:d}=t,{element:u,padding:f=0}=J(e,t)||{};if(u==null)return{};const p=Vn(f),g={x:n,y:r},m=At(o),x=St(m),h=await a.getDimensions(u),v=m==="y",y=v?"top":"left",b=v?"bottom":"right",E=v?"clientHeight":"clientWidth",w=s.reference[x]+s.reference[m]-g[m]-s.floating[x],C=g[m]-s.reference[m],A=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let S=A?A[E]:0;(!S||!await(a.isElement==null?void 0:a.isElement(A)))&&(S=c.floating[E]||s.floating[x]);const R=w/2-C/2,O=S/2-h[x]/2-1,P=ne(p[y],O),I=ne(p[b],O),F=P,T=S-h[x]-I,k=S/2-h[x]/2+R,z=ct(F,k,T),D=!d.arrow&&ge(o)!=null&&k!==z&&s.reference[x]/2-(k<F?P:I)-h[x]/2<0,_=D?k<F?k-F:k-T:0;return{[m]:g[m]+_,data:{[m]:z,centerOffset:k-z-_,...D&&{alignmentOffset:_}},reset:D}}}),sa=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:a,initialPlacement:c,platform:d,elements:u}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:g,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:h=!0,...v}=J(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const y=ee(o),b=re(c),E=ee(c)===c,w=await(d.isRTL==null?void 0:d.isRTL(u.floating)),C=g||(E||!h?[Be(c)]:Js(c)),A=x!=="none";!g&&A&&C.push(...ta(c,h,x,w));const S=[c,...C],R=await ye(t,v),O=[];let P=((r=s.flip)==null?void 0:r.overflows)||[];if(f&&O.push(R[y]),p){const k=Qs(o,a,w);O.push(R[k[0]],R[k[1]])}if(P=[...P,{placement:o,overflows:O}],!O.every(k=>k<=0)){var I,F;const k=(((I=s.flip)==null?void 0:I.index)||0)+1,z=S[k];if(z)return{data:{index:k,overflows:P},reset:{placement:z}};let D=(F=P.filter(_=>_.overflows[0]<=0).sort((_,j)=>_.overflows[1]-j.overflows[1])[0])==null?void 0:F.placement;if(!D)switch(m){case"bestFit":{var T;const _=(T=P.filter(j=>{if(A){const L=re(j.placement);return L===b||L==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(L=>L>0).reduce((L,q)=>L+q,0)]).sort((j,L)=>j[1]-L[1])[0])==null?void 0:T[0];_&&(D=_);break}case"initialPlacement":D=c;break}if(o!==D)return{reset:{placement:D}}}return{}}}};function rn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function on(e){return Gs.some(t=>e[t]>=0)}const aa=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=J(e,t);switch(r){case"referenceHidden":{const s=await ye(t,{...o,elementContext:"reference"}),a=rn(s,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:on(a)}}}case"escaped":{const s=await ye(t,{...o,altBoundary:!0}),a=rn(s,n.floating);return{data:{escapedOffsets:a,escaped:on(a)}}}default:return{}}}}};async function ia(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),a=ee(n),c=ge(n),d=re(n)==="y",u=["left","top"].includes(a)?-1:1,f=s&&d?-1:1,p=J(t,e);let{mainAxis:g,crossAxis:m,alignmentAxis:x}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return c&&typeof x=="number"&&(m=c==="end"?x*-1:x),d?{x:m*f,y:g*u}:{x:g*u,y:m*f}}const ca=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:a,middlewareData:c}=t,d=await ia(t,e);return a===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+d.x,y:s+d.y,data:{...d,placement:a}}}}},la=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:c={fn:v=>{let{x:y,y:b}=v;return{x:y,y:b}}},...d}=J(e,t),u={x:n,y:r},f=await ye(t,d),p=re(ee(o)),g=Ct(p);let m=u[g],x=u[p];if(s){const v=g==="y"?"top":"left",y=g==="y"?"bottom":"right",b=m+f[v],E=m-f[y];m=ct(b,m,E)}if(a){const v=p==="y"?"top":"left",y=p==="y"?"bottom":"right",b=x+f[v],E=x-f[y];x=ct(b,x,E)}const h=c.fn({...t,[g]:m,[p]:x});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[g]:s,[p]:a}}}}}},da=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:a}=t,{offset:c=0,mainAxis:d=!0,crossAxis:u=!0}=J(e,t),f={x:n,y:r},p=re(o),g=Ct(p);let m=f[g],x=f[p];const h=J(c,t),v=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(d){const E=g==="y"?"height":"width",w=s.reference[g]-s.floating[E]+v.mainAxis,C=s.reference[g]+s.reference[E]-v.mainAxis;m<w?m=w:m>C&&(m=C)}if(u){var y,b;const E=g==="y"?"width":"height",w=["top","left"].includes(ee(o)),C=s.reference[p]-s.floating[E]+(w&&((y=a.offset)==null?void 0:y[p])||0)+(w?0:v.crossAxis),A=s.reference[p]+s.reference[E]+(w?0:((b=a.offset)==null?void 0:b[p])||0)-(w?v.crossAxis:0);x<C?x=C:x>A&&(x=A)}return{[g]:m,[p]:x}}}},ua=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:a,elements:c}=t,{apply:d=()=>{},...u}=J(e,t),f=await ye(t,u),p=ee(o),g=ge(o),m=re(o)==="y",{width:x,height:h}=s.floating;let v,y;p==="top"||p==="bottom"?(v=p,y=g===(await(a.isRTL==null?void 0:a.isRTL(c.floating))?"start":"end")?"left":"right"):(y=p,v=g==="end"?"top":"bottom");const b=h-f.top-f.bottom,E=x-f.left-f.right,w=ne(h-f[v],b),C=ne(x-f[y],E),A=!t.middlewareData.shift;let S=w,R=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=E),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=b),A&&!g){const P=$(f.left,0),I=$(f.right,0),F=$(f.top,0),T=$(f.bottom,0);m?R=x-2*(P!==0||I!==0?P+I:$(f.left,f.right)):S=h-2*(F!==0||T!==0?F+T:$(f.top,f.bottom))}await d({...t,availableWidth:R,availableHeight:S});const O=await a.getDimensions(c.floating);return x!==O.width||h!==O.height?{reset:{rects:!0}}:{}}}};function ze(){return typeof window<"u"}function ve(e){return Un(e)?(e.nodeName||"").toLowerCase():"#document"}function H(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Q(e){var t;return(t=(Un(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Un(e){return ze()?e instanceof Node||e instanceof H(e).Node:!1}function V(e){return ze()?e instanceof Element||e instanceof H(e).Element:!1}function X(e){return ze()?e instanceof HTMLElement||e instanceof H(e).HTMLElement:!1}function sn(e){return!ze()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof H(e).ShadowRoot}function Ce(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=U(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function fa(e){return["table","td","th"].includes(ve(e))}function Ve(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Nt(e){const t=jt(),n=V(e)?U(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function ha(e){let t=oe(e);for(;X(t)&&!pe(t);){if(Nt(t))return t;if(Ve(t))return null;t=oe(t)}return null}function jt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function pe(e){return["html","body","#document"].includes(ve(e))}function U(e){return H(e).getComputedStyle(e)}function Ue(e){return V(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function oe(e){if(ve(e)==="html")return e;const t=e.assignedSlot||e.parentNode||sn(e)&&e.host||Q(e);return sn(t)?t.host:t}function Yn(e){const t=oe(e);return pe(t)?e.ownerDocument?e.ownerDocument.body:e.body:X(t)&&Ce(t)?t:Yn(t)}function be(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Yn(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),a=H(o);if(s){const c=dt(a);return t.concat(a,a.visualViewport||[],Ce(o)?o:[],c&&n?be(c):[])}return t.concat(o,be(o,[],n))}function dt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function qn(e){const t=U(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=X(e),s=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=Fe(n)!==s||Fe(r)!==a;return c&&(n=s,r=a),{width:n,height:r,$:c}}function Rt(e){return V(e)?e:e.contextElement}function fe(e){const t=Rt(e);if(!X(t))return G(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=qn(t);let a=(s?Fe(n.width):n.width)/r,c=(s?Fe(n.height):n.height)/o;return(!a||!Number.isFinite(a))&&(a=1),(!c||!Number.isFinite(c))&&(c=1),{x:a,y:c}}const pa=G(0);function Kn(e){const t=H(e);return!jt()||!t.visualViewport?pa:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function ma(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==H(e)?!1:t}function ae(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=Rt(e);let a=G(1);t&&(r?V(r)&&(a=fe(r)):a=fe(e));const c=ma(s,n,r)?Kn(s):G(0);let d=(o.left+c.x)/a.x,u=(o.top+c.y)/a.y,f=o.width/a.x,p=o.height/a.y;if(s){const g=H(s),m=r&&V(r)?H(r):r;let x=g,h=dt(x);for(;h&&r&&m!==x;){const v=fe(h),y=h.getBoundingClientRect(),b=U(h),E=y.left+(h.clientLeft+parseFloat(b.paddingLeft))*v.x,w=y.top+(h.clientTop+parseFloat(b.paddingTop))*v.y;d*=v.x,u*=v.y,f*=v.x,p*=v.y,d+=E,u+=w,x=H(h),h=dt(x)}}return We({width:f,height:p,x:d,y:u})}function Pt(e,t){const n=Ue(e).scrollLeft;return t?t.left+n:ae(Q(e)).left+n}function Gn(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Pt(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function ga(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",a=Q(r),c=t?Ve(t.floating):!1;if(r===a||c&&s)return n;let d={scrollLeft:0,scrollTop:0},u=G(1);const f=G(0),p=X(r);if((p||!p&&!s)&&((ve(r)!=="body"||Ce(a))&&(d=Ue(r)),X(r))){const m=ae(r);u=fe(r),f.x=m.x+r.clientLeft,f.y=m.y+r.clientTop}const g=a&&!p&&!s?Gn(a,d,!0):G(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-d.scrollLeft*u.x+f.x+g.x,y:n.y*u.y-d.scrollTop*u.y+f.y+g.y}}function va(e){return Array.from(e.getClientRects())}function xa(e){const t=Q(e),n=Ue(e),r=e.ownerDocument.body,o=$(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=$(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Pt(e);const c=-n.scrollTop;return U(r).direction==="rtl"&&(a+=$(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:a,y:c}}function ya(e,t){const n=H(e),r=Q(e),o=n.visualViewport;let s=r.clientWidth,a=r.clientHeight,c=0,d=0;if(o){s=o.width,a=o.height;const u=jt();(!u||u&&t==="fixed")&&(c=o.offsetLeft,d=o.offsetTop)}return{width:s,height:a,x:c,y:d}}function ba(e,t){const n=ae(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=X(e)?fe(e):G(1),a=e.clientWidth*s.x,c=e.clientHeight*s.y,d=o*s.x,u=r*s.y;return{width:a,height:c,x:d,y:u}}function an(e,t,n){let r;if(t==="viewport")r=ya(e,n);else if(t==="document")r=xa(Q(e));else if(V(t))r=ba(t,n);else{const o=Kn(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return We(r)}function Xn(e,t){const n=oe(e);return n===t||!V(n)||pe(n)?!1:U(n).position==="fixed"||Xn(n,t)}function wa(e,t){const n=t.get(e);if(n)return n;let r=be(e,[],!1).filter(c=>V(c)&&ve(c)!=="body"),o=null;const s=U(e).position==="fixed";let a=s?oe(e):e;for(;V(a)&&!pe(a);){const c=U(a),d=Nt(a);!d&&c.position==="fixed"&&(o=null),(s?!d&&!o:!d&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ce(a)&&!d&&Xn(e,a))?r=r.filter(f=>f!==a):o=c,a=oe(a)}return t.set(e,r),r}function Ea(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[...n==="clippingAncestors"?Ve(t)?[]:wa(t,this._c):[].concat(n),r],c=a[0],d=a.reduce((u,f)=>{const p=an(t,f,o);return u.top=$(p.top,u.top),u.right=ne(p.right,u.right),u.bottom=ne(p.bottom,u.bottom),u.left=$(p.left,u.left),u},an(t,c,o));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function Ca(e){const{width:t,height:n}=qn(e);return{width:t,height:n}}function Sa(e,t,n){const r=X(t),o=Q(t),s=n==="fixed",a=ae(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const d=G(0);if(r||!r&&!s)if((ve(t)!=="body"||Ce(o))&&(c=Ue(t)),r){const g=ae(t,!0,s,t);d.x=g.x+t.clientLeft,d.y=g.y+t.clientTop}else o&&(d.x=Pt(o));const u=o&&!r&&!s?Gn(o,c):G(0),f=a.left+c.scrollLeft-d.x-u.x,p=a.top+c.scrollTop-d.y-u.y;return{x:f,y:p,width:a.width,height:a.height}}function rt(e){return U(e).position==="static"}function cn(e,t){if(!X(e)||U(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Q(e)===n&&(n=n.ownerDocument.body),n}function Zn(e,t){const n=H(e);if(Ve(e))return n;if(!X(e)){let o=oe(e);for(;o&&!pe(o);){if(V(o)&&!rt(o))return o;o=oe(o)}return n}let r=cn(e,t);for(;r&&fa(r)&&rt(r);)r=cn(r,t);return r&&pe(r)&&rt(r)&&!Nt(r)?n:r||ha(e)||n}const Aa=async function(e){const t=this.getOffsetParent||Zn,n=this.getDimensions,r=await n(e.floating);return{reference:Sa(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Na(e){return U(e).direction==="rtl"}const ja={convertOffsetParentRelativeRectToViewportRelativeRect:ga,getDocumentElement:Q,getClippingRect:Ea,getOffsetParent:Zn,getElementRects:Aa,getClientRects:va,getDimensions:Ca,getScale:fe,isElement:V,isRTL:Na};function Qn(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ra(e,t){let n=null,r;const o=Q(e);function s(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function a(c,d){c===void 0&&(c=!1),d===void 0&&(d=1),s();const u=e.getBoundingClientRect(),{left:f,top:p,width:g,height:m}=u;if(c||t(),!g||!m)return;const x=Oe(p),h=Oe(o.clientWidth-(f+g)),v=Oe(o.clientHeight-(p+m)),y=Oe(f),E={rootMargin:-x+"px "+-h+"px "+-v+"px "+-y+"px",threshold:$(0,ne(1,d))||1};let w=!0;function C(A){const S=A[0].intersectionRatio;if(S!==d){if(!w)return a();S?a(!1,S):r=setTimeout(()=>{a(!1,1e-7)},1e3)}S===1&&!Qn(u,e.getBoundingClientRect())&&a(),w=!1}try{n=new IntersectionObserver(C,{...E,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,E)}n.observe(e)}return a(!0),s}function Pa(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:d=!1}=r,u=Rt(e),f=o||s?[...u?be(u):[],...be(t)]:[];f.forEach(y=>{o&&y.addEventListener("scroll",n,{passive:!0}),s&&y.addEventListener("resize",n)});const p=u&&c?Ra(u,n):null;let g=-1,m=null;a&&(m=new ResizeObserver(y=>{let[b]=y;b&&b.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var E;(E=m)==null||E.observe(t)})),n()}),u&&!d&&m.observe(u),m.observe(t));let x,h=d?ae(e):null;d&&v();function v(){const y=ae(e);h&&!Qn(h,y)&&n(),h=y,x=requestAnimationFrame(v)}return n(),()=>{var y;f.forEach(b=>{o&&b.removeEventListener("scroll",n),s&&b.removeEventListener("resize",n)}),p==null||p(),(y=m)==null||y.disconnect(),m=null,d&&cancelAnimationFrame(x)}}const ka=ca,Oa=la,Ta=sa,Da=ua,Ma=aa,ln=oa,_a=da,La=(e,t,n)=>{const r=new Map,o={platform:ja,...n},s={...o.platform,_c:r};return ra(e,t,{...o,platform:s})};var _e=typeof document<"u"?l.useLayoutEffect:l.useEffect;function $e(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!$e(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!$e(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Jn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function dn(e,t){const n=Jn(e);return Math.round(t*n)/n}function ot(e){const t=l.useRef(e);return _e(()=>{t.current=e}),t}function Ia(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:a}={},transform:c=!0,whileElementsMounted:d,open:u}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[g,m]=l.useState(r);$e(g,r)||m(r);const[x,h]=l.useState(null),[v,y]=l.useState(null),b=l.useCallback(j=>{j!==A.current&&(A.current=j,h(j))},[]),E=l.useCallback(j=>{j!==S.current&&(S.current=j,y(j))},[]),w=s||x,C=a||v,A=l.useRef(null),S=l.useRef(null),R=l.useRef(f),O=d!=null,P=ot(d),I=ot(o),F=ot(u),T=l.useCallback(()=>{if(!A.current||!S.current)return;const j={placement:t,strategy:n,middleware:g};I.current&&(j.platform=I.current),La(A.current,S.current,j).then(L=>{const q={...L,isPositioned:F.current!==!1};k.current&&!$e(R.current,q)&&(R.current=q,Wr.flushSync(()=>{p(q)}))})},[g,t,n,I,F]);_e(()=>{u===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,p(j=>({...j,isPositioned:!1})))},[u]);const k=l.useRef(!1);_e(()=>(k.current=!0,()=>{k.current=!1}),[]),_e(()=>{if(w&&(A.current=w),C&&(S.current=C),w&&C){if(P.current)return P.current(w,C,T);T()}},[w,C,T,P,O]);const z=l.useMemo(()=>({reference:A,floating:S,setReference:b,setFloating:E}),[b,E]),D=l.useMemo(()=>({reference:w,floating:C}),[w,C]),_=l.useMemo(()=>{const j={position:n,left:0,top:0};if(!D.floating)return j;const L=dn(D.floating,f.x),q=dn(D.floating,f.y);return c?{...j,transform:"translate("+L+"px, "+q+"px)",...Jn(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:L,top:q}},[n,c,D.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:T,refs:z,elements:D,floatingStyles:_}),[f,T,z,D,_])}const Fa=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ln({element:r.current,padding:o}).fn(n):{}:r?ln({element:r,padding:o}).fn(n):{}}}},Ba=(e,t)=>({...ka(e),options:[e,t]}),Wa=(e,t)=>({...Oa(e),options:[e,t]}),$a=(e,t)=>({..._a(e),options:[e,t]}),Ha=(e,t)=>({...Ta(e),options:[e,t]}),za=(e,t)=>({...Da(e),options:[e,t]}),Va=(e,t)=>({...Ma(e),options:[e,t]}),Ua=(e,t)=>({...Fa(e),options:[e,t]});var Ya="Arrow",er=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return i.jsx(W.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:i.jsx("polygon",{points:"0,0 30,0 15,10"})})});er.displayName=Ya;var qa=er,kt="Popper",[tr,nr]=mt(kt),[Ka,rr]=tr(kt),or=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return i.jsx(Ka,{scope:t,anchor:r,onAnchorChange:o,children:n})};or.displayName=kt;var sr="PopperAnchor",ar=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=rr(sr,n),a=l.useRef(null),c=Z(t,a);return l.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:i.jsx(W.div,{...o,ref:c})});ar.displayName=sr;var Ot="PopperContent",[Ga,Xa]=tr(Ot),ir=l.forwardRef((e,t)=>{var _t,Lt,It,Ft,Bt,Wt;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:g=!1,updatePositionStrategy:m="optimized",onPlaced:x,...h}=e,v=rr(Ot,n),[y,b]=l.useState(null),E=Z(t,xe=>b(xe)),[w,C]=l.useState(null),A=Ir(w),S=(A==null?void 0:A.width)??0,R=(A==null?void 0:A.height)??0,O=r+(s!=="center"?"-"+s:""),P=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},I=Array.isArray(u)?u:[u],F=I.length>0,T={padding:P,boundary:I.filter(Qa),altBoundary:F},{refs:k,floatingStyles:z,placement:D,isPositioned:_,middlewareData:j}=Ia({strategy:"fixed",placement:O,whileElementsMounted:(...xe)=>Pa(...xe,{animationFrame:m==="always"}),elements:{reference:v.anchor},middleware:[Ba({mainAxis:o+R,alignmentAxis:a}),d&&Wa({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?$a():void 0,...T}),d&&Ha({...T}),za({...T,apply:({elements:xe,rects:$t,availableWidth:Rr,availableHeight:Pr})=>{const{width:kr,height:Or}=$t.reference,Ne=xe.floating.style;Ne.setProperty("--radix-popper-available-width",`${Rr}px`),Ne.setProperty("--radix-popper-available-height",`${Pr}px`),Ne.setProperty("--radix-popper-anchor-width",`${kr}px`),Ne.setProperty("--radix-popper-anchor-height",`${Or}px`)}}),w&&Ua({element:w,padding:c}),Ja({arrowWidth:S,arrowHeight:R}),g&&Va({strategy:"referenceHidden",...T})]}),[L,q]=dr(D),Ae=he(x);Le(()=>{_&&(Ae==null||Ae())},[_,Ae]);const Cr=(_t=j.arrow)==null?void 0:_t.x,Sr=(Lt=j.arrow)==null?void 0:Lt.y,Ar=((It=j.arrow)==null?void 0:It.centerOffset)!==0,[Nr,jr]=l.useState();return Le(()=>{y&&jr(window.getComputedStyle(y).zIndex)},[y]),i.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:_?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Nr,"--radix-popper-transform-origin":[(Ft=j.transformOrigin)==null?void 0:Ft.x,(Bt=j.transformOrigin)==null?void 0:Bt.y].join(" "),...((Wt=j.hide)==null?void 0:Wt.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:i.jsx(Ga,{scope:n,placedSide:L,onArrowChange:C,arrowX:Cr,arrowY:Sr,shouldHideArrow:Ar,children:i.jsx(W.div,{"data-side":L,"data-align":q,...h,ref:E,style:{...h.style,animation:_?void 0:"none"}})})})});ir.displayName=Ot;var cr="PopperArrow",Za={top:"bottom",right:"left",bottom:"top",left:"right"},lr=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=Xa(cr,r),a=Za[s.placedSide];return i.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:i.jsx(qa,{...o,ref:n,style:{...o.style,display:"block"}})})});lr.displayName=cr;function Qa(e){return e!==null}var Ja=e=>({name:"transformOrigin",options:e,fn(t){var v,y,b;const{placement:n,rects:r,middlewareData:o}=t,a=((v=o.arrow)==null?void 0:v.centerOffset)!==0,c=a?0:e.arrowWidth,d=a?0:e.arrowHeight,[u,f]=dr(n),p={start:"0%",center:"50%",end:"100%"}[f],g=(((y=o.arrow)==null?void 0:y.x)??0)+c/2,m=(((b=o.arrow)==null?void 0:b.y)??0)+d/2;let x="",h="";return u==="bottom"?(x=a?p:`${g}px`,h=`${-d}px`):u==="top"?(x=a?p:`${g}px`,h=`${r.floating.height+d}px`):u==="right"?(x=`${-d}px`,h=a?p:`${m}px`):u==="left"&&(x=`${r.floating.width+d}px`,h=a?p:`${m}px`),{data:{x,y:h}}}});function dr(e){const[t,n="center"]=e.split("-");return[t,n]}var ei=or,ti=ar,ni=ir,ri=lr,oi="VisuallyHidden",ur=l.forwardRef((e,t)=>i.jsx(W.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ur.displayName=oi;var si=ur,[Ye,yc]=mt("Tooltip",[nr]),qe=nr(),fr="TooltipProvider",ai=700,ut="tooltip.open",[ii,Tt]=Ye(fr),hr=e=>{const{__scopeTooltip:t,delayDuration:n=ai,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[a,c]=l.useState(!0),d=l.useRef(!1),u=l.useRef(0);return l.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),i.jsx(ii,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(u.current),c(!1)},[]),onClose:l.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>c(!0),r)},[r]),isPointerInTransitRef:d,onPointerInTransitChange:l.useCallback(f=>{d.current=f},[]),disableHoverableContent:o,children:s})};hr.displayName=fr;var Ke="Tooltip",[ci,Se]=Ye(Ke),pr=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:a,delayDuration:c}=e,d=Tt(Ke,e.__scopeTooltip),u=qe(t),[f,p]=l.useState(null),g=Te(),m=l.useRef(0),x=a??d.disableHoverableContent,h=c??d.delayDuration,v=l.useRef(!1),[y=!1,b]=hn({prop:r,defaultProp:o,onChange:S=>{S?(d.onOpen(),document.dispatchEvent(new CustomEvent(ut))):d.onClose(),s==null||s(S)}}),E=l.useMemo(()=>y?v.current?"delayed-open":"instant-open":"closed",[y]),w=l.useCallback(()=>{window.clearTimeout(m.current),m.current=0,v.current=!1,b(!0)},[b]),C=l.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),A=l.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{v.current=!0,b(!0),m.current=0},h)},[h,b]);return l.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),i.jsx(ei,{...u,children:i.jsx(ci,{scope:t,contentId:g,open:y,stateAttribute:E,trigger:f,onTriggerChange:p,onTriggerEnter:l.useCallback(()=>{d.isOpenDelayed?A():w()},[d.isOpenDelayed,A,w]),onTriggerLeave:l.useCallback(()=>{x?C():(window.clearTimeout(m.current),m.current=0)},[C,x]),onOpen:w,onClose:C,disableHoverableContent:x,children:n})})};pr.displayName=Ke;var ft="TooltipTrigger",mr=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Se(ft,n),s=Tt(ft,n),a=qe(n),c=l.useRef(null),d=Z(t,c,o.onTriggerChange),u=l.useRef(!1),f=l.useRef(!1),p=l.useCallback(()=>u.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),i.jsx(ti,{asChild:!0,...a,children:i.jsx(W.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:d,onPointerMove:B(e.onPointerMove,g=>{g.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:B(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:B(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:B(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:B(e.onBlur,o.onClose),onClick:B(e.onClick,o.onClose)})})});mr.displayName=ft;var Dt="TooltipPortal",[li,di]=Ye(Dt,{forceMount:void 0}),gr=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,s=Se(Dt,t);return i.jsx(li,{scope:t,forceMount:n,children:i.jsx(Ee,{present:n||s.open,children:i.jsx(xt,{asChild:!0,container:o,children:r})})})};gr.displayName=Dt;var me="TooltipContent",vr=l.forwardRef((e,t)=>{const n=di(me,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,a=Se(me,e.__scopeTooltip);return i.jsx(Ee,{present:r||a.open,children:a.disableHoverableContent?i.jsx(xr,{side:o,...s,ref:t}):i.jsx(ui,{side:o,...s,ref:t})})}),ui=l.forwardRef((e,t)=>{const n=Se(me,e.__scopeTooltip),r=Tt(me,e.__scopeTooltip),o=l.useRef(null),s=Z(t,o),[a,c]=l.useState(null),{trigger:d,onClose:u}=n,f=o.current,{onPointerInTransitChange:p}=r,g=l.useCallback(()=>{c(null),p(!1)},[p]),m=l.useCallback((x,h)=>{const v=x.currentTarget,y={x:x.clientX,y:x.clientY},b=pi(y,v.getBoundingClientRect()),E=mi(y,b),w=gi(h.getBoundingClientRect()),C=xi([...E,...w]);c(C),p(!0)},[p]);return l.useEffect(()=>()=>g(),[g]),l.useEffect(()=>{if(d&&f){const x=v=>m(v,f),h=v=>m(v,d);return d.addEventListener("pointerleave",x),f.addEventListener("pointerleave",h),()=>{d.removeEventListener("pointerleave",x),f.removeEventListener("pointerleave",h)}}},[d,f,m,g]),l.useEffect(()=>{if(a){const x=h=>{const v=h.target,y={x:h.clientX,y:h.clientY},b=(d==null?void 0:d.contains(v))||(f==null?void 0:f.contains(v)),E=!vi(y,a);b?g():E&&(g(),u())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[d,f,a,u,g]),i.jsx(xr,{...e,ref:s})}),[fi,hi]=Ye(Ke,{isInside:!1}),xr=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:a,...c}=e,d=Se(me,n),u=qe(n),{onClose:f}=d;return l.useEffect(()=>(document.addEventListener(ut,f),()=>document.removeEventListener(ut,f)),[f]),l.useEffect(()=>{if(d.trigger){const p=g=>{const m=g.target;m!=null&&m.contains(d.trigger)&&f()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[d.trigger,f]),i.jsx(vt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:p=>p.preventDefault(),onDismiss:f,children:i.jsxs(ni,{"data-state":d.stateAttribute,...u,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[i.jsx(Mr,{children:r}),i.jsx(fi,{scope:n,isInside:!0,children:i.jsx(si,{id:d.contentId,role:"tooltip",children:o||r})})]})})});vr.displayName=me;var yr="TooltipArrow",br=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=qe(n);return hi(yr,n).isInside?null:i.jsx(ri,{...o,...r,ref:t})});br.displayName=yr;function pi(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function mi(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function gi(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function vi(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const c=t[s].x,d=t[s].y,u=t[a].x,f=t[a].y;d>r!=f>r&&n<(u-c)*(r-d)/(f-d)+c&&(o=!o)}return o}function xi(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),yi(t)}function yi(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],a=t[t.length-2];if((s.x-a.x)*(o.y-a.y)>=(s.y-a.y)*(o.x-a.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],a=n[n.length-2];if((s.x-a.x)*(o.y-a.y)>=(s.y-a.y)*(o.x-a.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var bi=hr,wi=pr,Ei=mr,Ci=gr,Si=vr,Ai=br;function wr({delayDuration:e=0,...t}){return i.jsx(bi,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Ni({...e}){return i.jsx(wr,{children:i.jsx(wi,{"data-slot":"tooltip",...e})})}function ji({...e}){return i.jsx(Ei,{"data-slot":"tooltip-trigger",...e})}function Ri({className:e,sideOffset:t=4,children:n,...r}){return i.jsx(Ci,{children:i.jsxs(Si,{"data-slot":"tooltip-content",sideOffset:t,className:N("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...r,children:[n,i.jsx(Ai,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Pi="sidebar_state",ki=60*60*24*7,Oi="16rem",Ti="18rem",Di="3rem",Mi="b",Er=l.createContext(null);function Mt(){const e=l.useContext(Er);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function _i({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:s,...a}){const c=wo(),[d,u]=l.useState(!1),[f,p]=l.useState(e),g=t??f,m=l.useCallback(y=>{const b=typeof y=="function"?y(g):y;n?n(b):p(b),document.cookie=`${Pi}=${b}; path=/; max-age=${ki}`},[n,g]),x=l.useCallback(()=>c?u(y=>!y):m(y=>!y),[c,m,u]);l.useEffect(()=>{const y=b=>{b.key===Mi&&(b.metaKey||b.ctrlKey)&&(b.preventDefault(),x())};return window.addEventListener("keydown",y),()=>window.removeEventListener("keydown",y)},[x]);const h=g?"expanded":"collapsed",v=l.useMemo(()=>({state:h,open:g,setOpen:m,isMobile:c,openMobile:d,setOpenMobile:u,toggleSidebar:x}),[h,g,m,c,d,u,x]);return i.jsx(Er.Provider,{value:v,children:i.jsx(wr,{delayDuration:0,children:i.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Oi,"--sidebar-width-icon":Di,...o},className:N("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...a,children:s})})})}function Li({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...s}){const{isMobile:a,state:c,openMobile:d,setOpenMobile:u}=Mt();return n==="none"?i.jsx("div",{"data-slot":"sidebar",className:N("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...s,children:o}):a?i.jsxs(Hs,{open:d,onOpenChange:u,...s,children:[i.jsxs(Ys,{className:"sr-only",children:[i.jsx(qs,{children:"Sidebar"}),i.jsx(Ks,{children:"Displays the mobile sidebar."})]}),i.jsx(Us,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Ti},side:e,children:i.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):i.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[i.jsx("div",{className:N("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),i.jsx("div",{className:N("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...s,children:i.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function Ii({className:e,onClick:t,...n}){const{toggleSidebar:r}=Mt();return i.jsxs(de,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:N("h-7 w-7",e),onClick:o=>{t==null||t(o),r()},...n,children:[i.jsx(oo,{}),i.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Fi({className:e,...t}){return i.jsx("main",{"data-slot":"sidebar-inset",className:N("bg-background relative flex max-w-full min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function Bi({className:e,...t}){return i.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:N("flex flex-col gap-2 p-2",e),...t})}function Wi({className:e,...t}){return i.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:N("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function $i({className:e,...t}){return i.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:N("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Hi({className:e,asChild:t=!1,...n}){const r=t?we:"div";return i.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:N("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function zi({className:e,...t}){return i.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:N("flex w-full min-w-0 flex-col gap-1",e),...t})}function Vi({className:e,...t}){return i.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:N("group/menu-item relative",e),...t})}const Ui=fn("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Yi({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:s,...a}){const c=e?we:"button",{isMobile:d,state:u}=Mt(),f=i.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:N(Ui({variant:n,size:r}),s),...a});return o?(typeof o=="string"&&(o={children:o}),i.jsxs(Ni,{children:[i.jsx(ji,{asChild:!0,children:f}),i.jsx(Ri,{side:"right",align:"center",hidden:u!=="collapsed"||d,...o})]})):f}function qi({variant:e="header",children:t,...n}){return e==="sidebar"?i.jsx(Fi,{...n,children:t}):i.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}function Ki({children:e,variant:t="default"}){return t==="sidebar"?i.jsx(_i,{children:i.jsx("div",{className:N("flex min-h-screen w-full"),children:e})}):i.jsx("div",{className:N("flex min-h-screen flex-col bg-unilink-lightest dark:bg-unilink-darkest"),children:e})}function Gi(){return i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-md bg-unilink-primary text-white",children:i.jsx(_r,{className:"size-5 fill-current"})}),i.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm",children:i.jsx("span",{className:"mb-0.5 truncate leading-tight font-semibold text-unilink-darkest dark:text-unilink-lightest",children:"UniLink"})})]})}function Xi({items:e=[]}){const t=Dr();return i.jsxs($i,{className:"px-2 py-0",children:[i.jsx(Hi,{children:"Platform"}),i.jsx(zi,{children:e.map(n=>i.jsx(Vi,{children:i.jsx(Yi,{asChild:!0,isActive:t.url.startsWith(n.href),tooltip:{children:n.title},children:i.jsxs(un,{href:n.href,prefetch:!0,children:[n.icon&&i.jsx(n.icon,{}),i.jsx("span",{children:n.title})]})})},n.title))})]})}const Zi={navMain:[{title:"Dashboard",href:"/dashboard",icon:eo},{title:"Feed",href:"/feed",icon:co},{title:"Announcements",href:"/announcements",icon:pn},{title:"Events",href:"/events",icon:zr},{title:"Organizations",href:"/organizations",icon:mo},{title:"Messages",href:"/messages",icon:no},{title:"Settings",href:"/settings",icon:uo}]};function Qi(){return i.jsxs(Li,{variant:"inset",children:[i.jsx(Bi,{children:i.jsx("div",{className:"flex items-center gap-2 px-2 py-2",children:i.jsx(Gi,{})})}),i.jsx(Wi,{children:i.jsx(Xi,{items:Zi.navMain})})]})}function Ji({...e}){return i.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function ec({className:e,...t}){return i.jsx("ol",{"data-slot":"breadcrumb-list",className:N("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function tc({className:e,...t}){return i.jsx("li",{"data-slot":"breadcrumb-item",className:N("inline-flex items-center gap-1.5",e),...t})}function nc({asChild:e,className:t,...n}){const r=e?we:"a";return i.jsx(r,{"data-slot":"breadcrumb-link",className:N("hover:text-foreground transition-colors",t),...n})}function rc({className:e,...t}){return i.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:N("text-foreground font-normal",e),...t})}function oc({children:e,className:t,...n}){return i.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:N("[&>svg]:size-3.5",t),...n,children:e??i.jsx(Ur,{})})}function sc({breadcrumbs:e}){return i.jsx(i.Fragment,{children:e.length>0&&i.jsx(Ji,{children:i.jsx(ec,{children:e.map((t,n)=>{const r=n===e.length-1;return i.jsxs(l.Fragment,{children:[i.jsx(tc,{children:r?i.jsx(rc,{children:t.title}):i.jsx(nc,{asChild:!0,children:i.jsx(un,{href:t.href,children:t.title})})}),!r&&i.jsx(oc,{})]},n)})})})})}function ac({breadcrumbs:e=[]}){return i.jsx("header",{className:"flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/50 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx(Ii,{className:"-ml-1"}),i.jsx(sc,{breadcrumbs:e})]})})}function ht({className:e,...t}){return i.jsx("div",{"data-slot":"card",className:N("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function bc({className:e,...t}){return i.jsx("div",{"data-slot":"card-header",className:N("flex flex-col gap-1.5 px-6",e),...t})}function wc({className:e,...t}){return i.jsx("div",{"data-slot":"card-title",className:N("leading-none font-semibold",e),...t})}function Ec({className:e,...t}){return i.jsx("div",{"data-slot":"card-description",className:N("text-muted-foreground text-sm",e),...t})}function pt({className:e,...t}){return i.jsx("div",{"data-slot":"card-content",className:N("px-6",e),...t})}function ic({onInstall:e,onDismiss:t}){const[n,r]=l.useState(null),[o,s]=l.useState(!1),[a,c]=l.useState(!1),[d,u]=l.useState(!1);l.useEffect(()=>{(()=>{const h=window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://");u(h),"getInstalledRelatedApps"in navigator&&navigator.getInstalledRelatedApps().then(v=>{c(v.length>0)})})();const m=h=>{h.preventDefault(),r(h),setTimeout(()=>{!a&&!d&&s(!0)},5e3)},x=()=>{console.log("PWA was installed"),c(!0),s(!1),r(null),e==null||e()};return window.addEventListener("beforeinstallprompt",m),window.addEventListener("appinstalled",x),()=>{window.removeEventListener("beforeinstallprompt",m),window.removeEventListener("appinstalled",x)}},[a,d,e]);const f=async()=>{if(n)try{await n.prompt();const{outcome:g}=await n.userChoice;console.log(g==="accepted"?"User accepted the install prompt":"User dismissed the install prompt"),r(null),s(!1)}catch(g){console.error("Error during installation:",g)}},p=()=>{s(!1),t==null||t(),sessionStorage.setItem("pwa-prompt-dismissed","true")};return a||d||!o||!n||sessionStorage.getItem("pwa-prompt-dismissed")?null:i.jsx("div",{className:"fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm",children:i.jsx(ht,{className:"border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50",children:i.jsxs(pt,{className:"p-4",children:[i.jsxs("div",{className:"flex items-start justify-between mb-3",children:[i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:i.jsx(Ht,{className:"w-5 h-5 text-blue-600"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold text-gray-900",children:"Install UniLink"}),i.jsx("p",{className:"text-sm text-gray-600",children:"Get the full app experience"})]})]}),i.jsx(de,{variant:"ghost",size:"sm",onClick:p,className:"h-6 w-6 p-0",children:i.jsx(gt,{className:"w-4 h-4"})})]}),i.jsx("div",{className:"space-y-3 mb-4",children:i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[i.jsxs("div",{className:"flex items-center gap-2 text-gray-600",children:[i.jsx(st,{className:"w-4 h-4 text-green-500"}),i.jsx("span",{children:"Works offline"})]}),i.jsxs("div",{className:"flex items-center gap-2 text-gray-600",children:[i.jsx(pn,{className:"w-4 h-4 text-blue-500"}),i.jsx("span",{children:"Push notifications"})]}),i.jsxs("div",{className:"flex items-center gap-2 text-gray-600",children:[i.jsx(bo,{className:"w-4 h-4 text-yellow-500"}),i.jsx("span",{children:"Faster loading"})]}),i.jsxs("div",{className:"flex items-center gap-2 text-gray-600",children:[i.jsx(ho,{className:"w-4 h-4 text-purple-500"}),i.jsx("span",{children:"Native feel"})]})]})}),i.jsxs("div",{className:"flex gap-2",children:[i.jsxs(de,{onClick:f,className:"flex-1",size:"sm",children:[i.jsx(Ht,{className:"w-4 h-4 mr-2"}),"Install App"]}),i.jsx(de,{variant:"outline",onClick:p,size:"sm",children:"Later"})]}),i.jsx("p",{className:"text-xs text-gray-500 mt-2 text-center",children:"Free • No app store required"})]})})})}const cc=fn("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-auto",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function lc({className:e,variant:t,asChild:n=!1,...r}){const o=n?we:"span";return i.jsx(o,{"data-slot":"badge",className:N(cc({variant:t}),e),...r})}function dc({className:e="",showDetails:t=!1}){const[n,r]=l.useState(navigator.onLine),[o,s]=l.useState(!1),[a,c]=l.useState(0),[d,u]=l.useState(null);l.useEffect(()=>{const g=()=>{r(!0),s(!1),"serviceWorker"in navigator&&"sync"in window.ServiceWorkerRegistration.prototype&&navigator.serviceWorker.ready.then(v=>v.sync.register("background-sync-posts")).then(()=>navigator.serviceWorker.ready).then(v=>v.sync.register("background-sync-reactions")).catch(v=>{console.error("Background sync registration failed:",v)}),u(new Date)},m=()=>{r(!1),s(!0)},x=()=>{const v=parseInt(localStorage.getItem("pending-actions")||"0");c(v)};window.addEventListener("online",g),window.addEventListener("offline",m);const h=setInterval(x,5e3);return x(),()=>{window.removeEventListener("online",g),window.removeEventListener("offline",m),clearInterval(h)}},[]);const f=()=>{fetch("/api/v1/health",{method:"HEAD"}).then(()=>{r(!0),s(!1)}).catch(()=>{r(!1)})},p=()=>{s(!1)};return t?!n&&o?i.jsx("div",{className:"fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-md",children:i.jsx(ht,{className:"border-orange-200 bg-orange-50",children:i.jsxs(pt,{className:"p-4",children:[i.jsxs("div",{className:"flex items-start justify-between mb-3",children:[i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx(zt,{className:"w-5 h-5 text-orange-600"}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold text-orange-900",children:"You're offline"}),i.jsx("p",{className:"text-sm text-orange-700",children:"Some features may be limited"})]})]}),i.jsx(de,{variant:"ghost",size:"sm",onClick:p,className:"h-6 w-6 p-0",children:i.jsx(gt,{className:"w-4 h-4"})})]}),i.jsxs("div",{className:"space-y-2 mb-4",children:[i.jsxs("div",{className:"flex items-center gap-2 text-sm text-orange-700",children:[i.jsx(Gr,{className:"w-4 h-4"}),i.jsx("span",{children:"You can still browse cached content"})]}),i.jsxs("div",{className:"flex items-center gap-2 text-sm text-orange-700",children:[i.jsx(Zr,{className:"w-4 h-4"}),i.jsx("span",{children:"Actions will sync when you're back online"})]}),a>0&&i.jsxs("div",{className:"flex items-center gap-2 text-sm text-orange-700",children:[i.jsx(qr,{className:"w-4 h-4"}),i.jsxs("span",{children:[a," actions waiting to sync"]})]})]}),i.jsxs(de,{onClick:f,variant:"outline",size:"sm",className:"w-full",children:[i.jsx(ao,{className:"w-4 h-4 mr-2"}),"Check Connection"]})]})})}):n&&(a>0||d)?i.jsx("div",{className:"fixed bottom-4 right-4 z-40",children:i.jsx(ht,{className:"border-green-200 bg-green-50",children:i.jsx(pt,{className:"p-3",children:i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx(st,{className:"w-4 h-4 text-green-600"}),i.jsxs("div",{className:"text-sm",children:[i.jsx("div",{className:"font-medium text-green-900",children:"Back online"}),a>0?i.jsxs("div",{className:"text-green-700",children:["Syncing ",a," actions..."]}):d?i.jsxs("div",{className:"text-green-700",children:["Synced ",d.toLocaleTimeString()]}):null]})]})})})}):null:i.jsxs("div",{className:`flex items-center gap-2 ${e}`,children:[n?i.jsxs("div",{className:"flex items-center gap-1 text-green-600",children:[i.jsx(st,{className:"w-4 h-4"}),i.jsx("span",{className:"text-sm",children:"Online"})]}):i.jsxs("div",{className:"flex items-center gap-1 text-red-600",children:[i.jsx(zt,{className:"w-4 h-4"}),i.jsx("span",{className:"text-sm",children:"Offline"})]}),a>0&&i.jsxs(lc,{variant:"outline",className:"text-xs",children:[a," pending"]})]})}function uc({children:e,breadcrumbs:t=[]}){return i.jsxs(Ki,{variant:"sidebar",children:[i.jsx(Qi,{}),i.jsxs(qi,{variant:"sidebar",className:"overflow-x-hidden",children:[i.jsx(ac,{breadcrumbs:t}),e]}),i.jsx(ic,{}),i.jsx(dc,{showDetails:!0})]})}const Cc=({children:e,breadcrumbs:t,...n})=>i.jsx(uc,{breadcrumbs:t,...n,children:e});export{Cc as A,lc as B,zr as C,Ht as D,vn as F,Is as O,xt as P,ei as R,uo as S,xc as T,mo as U,ur as V,gt as X,Zr as a,ht as b,bc as c,wc as d,pt as e,qr as f,Ec as g,nr as h,ti as i,Ns as j,Wo as k,Nn as l,vt as m,ni as n,ri as o,_s as p,Fs as q,$s as r,Bs as s,Ws as t,Te as u,Ls as v};
