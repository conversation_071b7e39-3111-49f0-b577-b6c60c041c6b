import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    Edit,
    MapPin,
    Calendar,
    Mail,
    Phone,
    Globe,
    Users,
    BookOpen,
    MessageCircle,
    UserPlus,
    Settings
} from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    bio?: string;
    location?: string;
    website?: string;
    phone?: string;
    student_id?: string;
    department?: string;
    year_level?: string;
    campus: string;
    joined_at: string;
    posts_count: number;
    followers_count: number;
    following_count: number;
    organizations_count: number;
    is_following?: boolean;
    is_own_profile: boolean;
}

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
}

interface ProfileShowProps {
    userId?: string;
    user?: User;
    posts?: Post[];
}

export default function ProfileShow({ userId, user: initialUser, posts: initialPosts }: ProfileShowProps) {
    const [user, setUser] = useState<User | null>(initialUser || null);
    const [posts, setPosts] = useState<Post[]>(initialPosts || []);
    const [loading, setLoading] = useState(!initialUser);
    const [postsLoading, setPostsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState('posts');

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Profile',
            href: userId ? `/profile/${userId}` : '/profile',
        },
    ];

    // Load user profile if not provided
    useEffect(() => {
        if (!initialUser) {
            loadUser();
        }
        if (!initialPosts) {
            loadUserPosts();
        }
    }, [userId]);

    const loadUser = async () => {
        setLoading(true);
        setError(null);
        try {
            const url = userId ? `/api/v1/users/${userId}` : '/api/v1/user/profile';
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setUser(data);
            } else if (response.status === 404) {
                setError('User not found');
            } else {
                setError('Failed to load profile');
            }
        } catch (error) {
            console.error('Error loading user:', error);
            setError('An unexpected error occurred');
        } finally {
            setLoading(false);
        }
    };

    const loadUserPosts = async () => {
        setPostsLoading(true);
        try {
            const url = userId ? `/api/v1/users/${userId}/posts` : '/api/v1/user/posts';
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setPosts(data.data || []);
            }
        } catch (error) {
            console.error('Error loading user posts:', error);
        } finally {
            setPostsLoading(false);
        }
    };

    const handleFollow = async () => {
        if (!user || user.is_own_profile) return;

        try {
            const response = await fetch(`/api/v1/users/${user.id}/follow`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                setUser(prev => prev ? {
                    ...prev,
                    is_following: !prev.is_following,
                    followers_count: prev.is_following ? prev.followers_count - 1 : prev.followers_count + 1
                } : null);
            }
        } catch (error) {
            console.error('Error following user:', error);
        }
    };

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { 
                                ...post, 
                                reactions_count: post.user_reaction === reactionType 
                                    ? post.reactions_count - 1 
                                    : post.reactions_count + (post.user_reaction ? 0 : 1),
                                user_reaction: post.user_reaction === reactionType ? undefined : reactionType
                            }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        navigator.share?.({
            title: 'UniLink Post',
            url: `${window.location.origin}/posts/${postId}`
        });
    };

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Loading..." />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="text-center py-5">
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <p className="mt-3 text-muted">Loading profile...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (error || !user) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Error" />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="card shadow-sm">
                                <div className="card-body text-center py-5">
                                    <h3 className="h5 mb-3">Profile Not Found</h3>
                                    <p className="text-muted mb-4">
                                        {error || 'The profile you are looking for does not exist.'}
                                    </p>
                                    <Link href="/dashboard" className="btn btn-primary">
                                        Back to Dashboard
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={user.name} />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        {/* Profile Header */}
                        <div className="card shadow-sm mb-4">
                            <div className="card-body">
                                <div className="row align-items-center">
                                    <div className="col-md-3 text-center">
                                        <img
                                            src={user.avatar || '/img/profilepic.jpg'}
                                            alt={user.name}
                                            className="rounded-circle mb-3"
                                            width="120"
                                            height="120"
                                            style={{objectFit: 'cover'}}
                                        />
                                    </div>
                                    <div className="col-md-6">
                                        <h1 className="h3 mb-2">{user.name}</h1>
                                        {user.student_id && (
                                            <p className="text-muted mb-2">Student ID: {user.student_id}</p>
                                        )}
                                        {user.bio && (
                                            <p className="text-muted mb-3">{user.bio}</p>
                                        )}
                                        
                                        <div className="row g-2 text-muted small">
                                            {user.department && (
                                                <div className="col-md-6">
                                                    <BookOpen className="w-4 h-4 me-1" />
                                                    {user.department}
                                                </div>
                                            )}
                                            {user.location && (
                                                <div className="col-md-6">
                                                    <MapPin className="w-4 h-4 me-1" />
                                                    {user.location}
                                                </div>
                                            )}
                                            <div className="col-md-6">
                                                <Calendar className="w-4 h-4 me-1" />
                                                Joined {new Date(user.joined_at).toLocaleDateString()}
                                            </div>
                                            {user.website && (
                                                <div className="col-md-6">
                                                    <Globe className="w-4 h-4 me-1" />
                                                    <a href={user.website} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                                                        Website
                                                    </a>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="col-md-3 text-center">
                                        {user.is_own_profile ? (
                                            <div className="d-flex flex-column gap-2">
                                                <Link href="/profile/edit" className="btn btn-outline-primary">
                                                    <Edit className="w-4 h-4 me-2" />
                                                    Edit Profile
                                                </Link>
                                                <Link href="/settings" className="btn btn-outline-secondary">
                                                    <Settings className="w-4 h-4 me-2" />
                                                    Settings
                                                </Link>
                                            </div>
                                        ) : (
                                            <div className="d-flex flex-column gap-2">
                                                <button
                                                    onClick={handleFollow}
                                                    className={`btn ${user.is_following ? 'btn-outline-primary' : 'btn-primary'}`}
                                                >
                                                    <UserPlus className="w-4 h-4 me-2" />
                                                    {user.is_following ? 'Unfollow' : 'Follow'}
                                                </button>
                                                <button className="btn btn-outline-secondary">
                                                    <MessageCircle className="w-4 h-4 me-2" />
                                                    Message
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Stats */}
                                <div className="row text-center mt-4 pt-3 border-top">
                                    <div className="col-3">
                                        <div className="h5 mb-0">{user.posts_count}</div>
                                        <small className="text-muted">Posts</small>
                                    </div>
                                    <div className="col-3">
                                        <div className="h5 mb-0">{user.followers_count}</div>
                                        <small className="text-muted">Followers</small>
                                    </div>
                                    <div className="col-3">
                                        <div className="h5 mb-0">{user.following_count}</div>
                                        <small className="text-muted">Following</small>
                                    </div>
                                    <div className="col-3">
                                        <div className="h5 mb-0">{user.organizations_count}</div>
                                        <small className="text-muted">Organizations</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Content Tabs */}
                        <div className="card shadow-sm">
                            <div className="card-header">
                                <ul className="nav nav-tabs card-header-tabs">
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'posts' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('posts')}
                                        >
                                            Posts ({user.posts_count})
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'organizations' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('organizations')}
                                        >
                                            Organizations ({user.organizations_count})
                                        </button>
                                    </li>
                                    {user.is_own_profile && (
                                        <li className="nav-item">
                                            <button
                                                className={`nav-link ${activeTab === 'activity' ? 'active' : ''}`}
                                                onClick={() => setActiveTab('activity')}
                                            >
                                                Activity
                                            </button>
                                        </li>
                                    )}
                                </ul>
                            </div>
                            <div className="card-body">
                                {activeTab === 'posts' && (
                                    <div>
                                        {postsLoading ? (
                                            <div className="text-center py-4">
                                                <div className="spinner-border text-primary" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                        ) : posts.length > 0 ? (
                                            <div className="space-y-4">
                                                {posts.map((post) => (
                                                    <PostCard
                                                        key={post.id}
                                                        post={post}
                                                        onReact={handleReaction}
                                                        onComment={handleComment}
                                                        onShare={handleShare}
                                                    />
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="text-center py-5">
                                                <BookOpen className="w-12 h-12 text-muted mx-auto mb-3" />
                                                <h5 className="mb-2">No posts yet</h5>
                                                <p className="text-muted">
                                                    {user.is_own_profile 
                                                        ? "You haven't created any posts yet." 
                                                        : `${user.name} hasn't posted anything yet.`}
                                                </p>
                                                {user.is_own_profile && (
                                                    <Link href="/posts/create" className="btn btn-primary mt-3">
                                                        Create Your First Post
                                                    </Link>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}

                                {activeTab === 'organizations' && (
                                    <div className="text-center py-5">
                                        <Users className="w-12 h-12 text-muted mx-auto mb-3" />
                                        <h5 className="mb-2">Organizations</h5>
                                        <p className="text-muted">Organization memberships will be shown here.</p>
                                    </div>
                                )}

                                {activeTab === 'activity' && user.is_own_profile && (
                                    <div className="text-center py-5">
                                        <Calendar className="w-12 h-12 text-muted mx-auto mb-3" />
                                        <h5 className="mb-2">Recent Activity</h5>
                                        <p className="text-muted">Your recent activity will be shown here.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
