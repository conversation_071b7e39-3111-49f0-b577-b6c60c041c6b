import{j as e,r as p,L as O,S as g}from"./app-FHRWCuO9.js";import{A as D,f as P,b as u,c as j,d as f,g as b,e as v}from"./app-layout-CQXuNuUM.js";import{c as S,b as _,B as y}from"./app-logo-icon-CDT9SgfN.js";import{I as x}from"./input-B0Iek5Am.js";import{T as F}from"./textarea-oaTM8Zk4.js";import{L as n}from"./label-BnjRDuBj.js";import{A as E}from"./arrow-left-B5UQXdDX.js";import"./index-CZG6ZiXE.js";import"./index-BJKo6RB-.js";const I=_("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function L({className:a,variant:o,...s}){return e.jsx("div",{"data-slot":"alert",role:"alert",className:S(I({variant:o}),a),...s})}function k({className:a,...o}){return e.jsx("div",{"data-slot":"alert-description",className:S("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...o})}function K(){const[a,o]=p.useState({name:"",description:"",type:"club",email:"",phone:"",website:"",address:"",campus:"",logo:null,banner_image:null}),[s,d]=p.useState({}),[N,C]=p.useState(!1),l=c=>{const{name:r,value:t}=c.target;o(i=>({...i,[r]:t})),s[r]&&d(i=>({...i,[r]:[]}))},w=c=>{const{name:r,files:t}=c.target;t&&t[0]&&(o(i=>({...i,[r]:t[0]})),s[r]&&d(i=>({...i,[r]:[]})))},A=async c=>{var r;c.preventDefault(),C(!0),d({});try{const t=new FormData;Object.entries(a).forEach(([z,m])=>{m!==null&&m!==""&&(m instanceof File?t.append(z,m):t.append(z,m.toString()))});const i=await fetch("/api/v1/organizations",{method:"POST",body:t,headers:{"X-CSRF-TOKEN":((r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content"))||""}}),h=await i.json();i.ok?g.visit(`/organizations/${h.organization.id}`,{onSuccess:()=>{}}):h.errors?d(h.errors):d({general:[h.message||"An error occurred"]})}catch(t){console.error("Error creating organization:",t),d({general:["An unexpected error occurred. Please try again."]})}finally{C(!1)}};return e.jsxs(D,{children:[e.jsx(O,{title:"Create Organization"}),e.jsx("div",{className:"py-12",children:e.jsx("div",{className:"max-w-4xl mx-auto sm:px-6 lg:px-8",children:e.jsx("div",{className:"bg-white overflow-hidden shadow-sm sm:rounded-lg",children:e.jsxs("div",{className:"p-6 text-gray-900",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsxs(y,{variant:"ghost",size:"sm",onClick:()=>g.visit("/organizations"),className:"mr-4",children:[e.jsx(E,{className:"w-4 h-4 mr-2"}),"Back to Organizations"]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Create Organization"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Start a new organization to connect with fellow students"})]})]}),s.general&&e.jsxs(L,{className:"mb-6 border-red-200 bg-red-50",children:[e.jsx(P,{className:"h-4 w-4 text-red-600"}),e.jsx(k,{className:"text-red-800",children:s.general[0]})]}),e.jsxs("form",{onSubmit:A,className:"space-y-6",children:[e.jsxs(u,{children:[e.jsxs(j,{children:[e.jsx(f,{children:"Basic Information"}),e.jsx(b,{children:"Provide the essential details about your organization"})]}),e.jsxs(v,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"name",children:"Organization Name *"}),e.jsx(x,{id:"name",name:"name",type:"text",value:a.name,onChange:l,placeholder:"Enter organization name",className:s.name?"border-red-500":"",required:!0}),s.name&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.name[0]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"description",children:"Description"}),e.jsx(F,{id:"description",name:"description",value:a.description,onChange:l,placeholder:"Describe your organization's mission and activities",rows:4,className:s.description?"border-red-500":""}),s.description&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.description[0]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"type",children:"Organization Type *"}),e.jsxs("select",{id:"type",name:"type",value:a.type,onChange:l,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,children:[e.jsx("option",{value:"club",children:"Club"}),e.jsx("option",{value:"society",children:"Society"}),e.jsx("option",{value:"department",children:"Department"}),e.jsx("option",{value:"committee",children:"Committee"}),e.jsx("option",{value:"other",children:"Other"})]}),s.type&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.type[0]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"campus",children:"Campus"}),e.jsx(x,{id:"campus",name:"campus",type:"text",value:a.campus,onChange:l,placeholder:"Main Campus, North Campus, etc.",className:s.campus?"border-red-500":""}),s.campus&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.campus[0]})]})]})]})]}),e.jsxs(u,{children:[e.jsxs(j,{children:[e.jsx(f,{children:"Contact Information"}),e.jsx(b,{children:"How can people reach your organization?"})]}),e.jsxs(v,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"email",children:"Email"}),e.jsx(x,{id:"email",name:"email",type:"email",value:a.email,onChange:l,placeholder:"<EMAIL>",className:s.email?"border-red-500":""}),s.email&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.email[0]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"phone",children:"Phone"}),e.jsx(x,{id:"phone",name:"phone",type:"tel",value:a.phone,onChange:l,placeholder:"+****************",className:s.phone?"border-red-500":""}),s.phone&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.phone[0]})]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"website",children:"Website"}),e.jsx(x,{id:"website",name:"website",type:"url",value:a.website,onChange:l,placeholder:"https://yourorganization.com",className:s.website?"border-red-500":""}),s.website&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.website[0]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"address",children:"Address"}),e.jsx(F,{id:"address",name:"address",value:a.address,onChange:l,placeholder:"Physical address or meeting location",rows:2,className:s.address?"border-red-500":""}),s.address&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.address[0]})]})]})]}),e.jsxs(u,{children:[e.jsxs(j,{children:[e.jsx(f,{children:"Media"}),e.jsx(b,{children:"Upload images to represent your organization"})]}),e.jsxs(v,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"logo",children:"Logo"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("input",{id:"logo",name:"logo",type:"file",accept:"image/*",onChange:w,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"PNG, JPG, GIF up to 2MB"})]}),s.logo&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.logo[0]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"banner_image",children:"Banner Image"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("input",{id:"banner_image",name:"banner_image",type:"file",accept:"image/*",onChange:w,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"PNG, JPG, GIF up to 5MB"})]}),s.banner_image&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s.banner_image[0]})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(y,{type:"button",variant:"outline",onClick:()=>g.visit("/organizations"),children:"Cancel"}),e.jsx(y,{type:"submit",disabled:N,children:N?"Creating...":"Create Organization"})]})]})]})})})})]})}export{K as default};
