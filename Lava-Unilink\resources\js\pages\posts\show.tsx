import { useState, useEffect } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import CommentsSection from '@/components/comments-section';
import { type BreadcrumbItem } from '@/types';
import {
    ArrowLeft,
    Share2,
    MoreHorizontal,
    Edit,
    Trash2,
    Flag
} from 'lucide-react';

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
    can_edit?: boolean;
    can_delete?: boolean;
}

interface PostShowProps {
    postId: string;
    post?: Post;
}

export default function PostShow({ postId, post: initialPost }: PostShowProps) {
    const [post, setPost] = useState<Post | null>(initialPost || null);
    const [loading, setLoading] = useState(!initialPost);
    const [error, setError] = useState<string | null>(null);
    const [showActions, setShowActions] = useState(false);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Posts',
            href: '/posts',
        },
        {
            title: post?.title || 'Post',
            href: `/posts/${postId}`,
        },
    ];

    // Load post if not provided
    useEffect(() => {
        if (!initialPost) {
            loadPost();
        }
    }, [postId]);

    const loadPost = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await fetch(`/api/v1/posts/${postId}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setPost(data);
            } else if (response.status === 404) {
                setError('Post not found');
            } else {
                setError('Failed to load post');
            }
        } catch (error) {
            console.error('Error loading post:', error);
            setError('An unexpected error occurred');
        } finally {
            setLoading(false);
        }
    };

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        if (!post) return;

        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPost(prevPost => {
                    if (!prevPost) return null;
                    return {
                        ...prevPost,
                        reactions_count: prevPost.user_reaction === reactionType 
                            ? prevPost.reactions_count - 1 
                            : prevPost.reactions_count + (prevPost.user_reaction ? 0 : 1),
                        user_reaction: prevPost.user_reaction === reactionType ? undefined : reactionType
                    };
                });
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        // Scroll to comments section
        const commentsSection = document.getElementById('comments');
        if (commentsSection) {
            commentsSection.scrollIntoView({ behavior: 'smooth' });
        }
    };

    const handleShare = async (postId: number) => {
        const url = `${window.location.origin}/posts/${postId}`;
        
        if (navigator.share) {
            try {
                await navigator.share({
                    title: post?.title,
                    text: post?.content.substring(0, 100) + '...',
                    url: url
                });
            } catch (error) {
                // User cancelled sharing
            }
        } else {
            // Fallback: copy to clipboard
            try {
                await navigator.clipboard.writeText(url);
                // Show success message
                alert('Link copied to clipboard!');
            } catch (error) {
                console.error('Failed to copy link:', error);
            }
        }
    };

    const handleDelete = async () => {
        if (!post || !confirm('Are you sure you want to delete this post?')) return;

        try {
            const response = await fetch(`/api/v1/posts/${post.id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                window.location.href = '/posts';
            } else {
                alert('Failed to delete post');
            }
        } catch (error) {
            console.error('Error deleting post:', error);
            alert('An error occurred while deleting the post');
        }
    };

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Loading..." />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="row">
                                <div className="col-md-8 mx-auto">
                                    <div className="text-center py-5">
                                        <div className="spinner-border text-primary" role="status">
                                            <span className="visually-hidden">Loading...</span>
                                        </div>
                                        <p className="mt-3 text-muted">Loading post...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (error || !post) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Error" />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="row">
                                <div className="col-md-8 mx-auto">
                                    <div className="card shadow-sm">
                                        <div className="card-body text-center py-5">
                                            <h3 className="h5 mb-3">Post Not Found</h3>
                                            <p className="text-muted mb-4">
                                                {error || 'The post you are looking for does not exist or has been removed.'}
                                            </p>
                                            <Link href="/posts" className="btn btn-primary">
                                                <ArrowLeft className="w-4 h-4 me-2" />
                                                Back to Posts
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={post.title} />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        <div className="row">
                            <div className="col-md-8 mx-auto">
                                {/* Header */}
                                <div className="d-flex align-items-center justify-content-between mb-4">
                                    <Link href="/posts" className="btn btn-outline-secondary">
                                        <ArrowLeft className="w-4 h-4 me-2" />
                                        Back to Posts
                                    </Link>

                                    {/* Actions Dropdown */}
                                    <div className="dropdown">
                                        <button
                                            className="btn btn-outline-secondary"
                                            type="button"
                                            onClick={() => setShowActions(!showActions)}
                                        >
                                            <MoreHorizontal className="w-4 h-4" />
                                        </button>
                                        {showActions && (
                                            <div className="dropdown-menu dropdown-menu-end show">
                                                <button
                                                    className="dropdown-item"
                                                    onClick={() => handleShare(post.id)}
                                                >
                                                    <Share2 className="w-4 h-4 me-2" />
                                                    Share
                                                </button>
                                                {post.can_edit && (
                                                    <Link href={`/posts/${post.id}/edit`} className="dropdown-item">
                                                        <Edit className="w-4 h-4 me-2" />
                                                        Edit
                                                    </Link>
                                                )}
                                                {post.can_delete && (
                                                    <>
                                                        <div className="dropdown-divider"></div>
                                                        <button
                                                            className="dropdown-item text-danger"
                                                            onClick={handleDelete}
                                                        >
                                                            <Trash2 className="w-4 h-4 me-2" />
                                                            Delete
                                                        </button>
                                                    </>
                                                )}
                                                {!post.can_edit && !post.can_delete && (
                                                    <button className="dropdown-item">
                                                        <Flag className="w-4 h-4 me-2" />
                                                        Report
                                                    </button>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Post Content */}
                                <PostCard
                                    post={post}
                                    onReact={handleReaction}
                                    onComment={handleComment}
                                    onShare={handleShare}
                                />

                                {/* Comments Section */}
                                {post.comments_enabled && (
                                    <div id="comments" className="mt-4">
                                        <CommentsSection postId={post.id} />
                                    </div>
                                )}

                                {/* Related Posts */}
                                <div className="mt-5">
                                    <h4 className="h5 mb-3">Related Posts</h4>
                                    <div className="card shadow-sm">
                                        <div className="card-body text-center py-4">
                                            <p className="text-muted mb-0">Related posts will be shown here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
