import{r as n,j as o}from"./app-BYRFhzWn.js";import{P as s}from"./index-BNlTrGhe.js";import{c as i}from"./app-logo-icon-BTP1W0LK.js";var d="Label",l=n.forwardRef((e,a)=>o.jsx(s.label,{...e,ref:a,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||((r=e.onMouseDown)==null||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName=d;var u=l;function x({className:e,...a}){return o.jsx(u,{"data-slot":"label",className:i("text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}export{x as L};
