import{r as a,j as e,t as be,K as ve,L as ye,$ as U}from"./app-2kjRWvs5.js";import{b as se,c as Ne,d as we,B as $,X as I,e as Se,U as X,A as Ce,C as _e,S as ke}from"./app-layout-Susg8ZLQ.js";import{P as ze}from"./post-card-C5HqtaPZ.js";import{a as Y,B as E,c as J}from"./app-logo-icon-ylztpkIq.js";import{I as W}from"./input-YDbrhX2S.js";import{L as S}from"./label-RiMQ-CEH.js";import{S as L,a as D,b as M,c as B,d as F}from"./select-DSchLgT0.js";import{F as ae}from"./filter-CmaVTpsL.js";import{D as Pe,a as Fe,b as Ee,c as Ae}from"./dialog-BmRT_9z-.js";import{T as Oe}from"./textarea-DeEzhama.js";import"./index-CdTsfyPs.js";import{F as K}from"./file-text-CjhxN0YP.js";import{T as Te}from"./trash-2-Cgjhu2W9.js";import{G as Re}from"./globe-C32ZNMlR.js";import{L as $e}from"./lock-6QWOr0qc.js";import{P as Z}from"./plus-CacFV2l9.js";import"./index-8HzOgwF5.js";import"./pin-CmA81IO2.js";import"./message-circle-DHVGmBb0.js";import"./index-BfgaSTtC.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],Le=Y("Image",Ie);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Me=Y("RotateCcw",De);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],Ve=Y("Upload",Be),ee=[{value:"announcement",label:"Announcements"},{value:"discussion",label:"Discussions"},{value:"event",label:"Events"},{value:"news",label:"News"}],Ue=[{value:"created_at",label:"Latest"},{value:"popularity",label:"Most Popular"},{value:"trending",label:"Trending"},{value:"reactions_count",label:"Most Liked"},{value:"comments_count",label:"Most Discussed"}],We=["Main Campus","Isulan Campus","ACCESS Campus","Tacurong Campus","Bagumbayan Campus"];function qe({filters:t,onFiltersChange:o,onClose:s}){var w,k;const[i,n]=a.useState(t),[c,d]=a.useState([]),[u,h]=a.useState(!1);a.useEffect(()=>{(async()=>{var j;h(!0);try{const P=await fetch("/api/v1/organizations?per_page=100",{headers:{Authorization:`Bearer ${(j=document.querySelector('meta[name="api-token"]'))==null?void 0:j.getAttribute("content")}`,Accept:"application/json"}});if(P.ok){const r=await P.json();d(r.data||[])}}catch(P){console.error("Error loading organizations:",P)}finally{h(!1)}})()},[]);const p=(l,j)=>{const P=j==="all"||j==="personal"?"":j;n(r=>({...r,[l]:P}))},m=()=>{o(i),s()},x=()=>{const l={type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"};n(l),o(l)},g=()=>Object.values(i).filter(l=>l!==""&&l!=="created_at"&&l!=="desc").length,f=l=>{const j={...i};l==="sort_by"?j[l]="created_at":l==="sort_order"?j[l]="desc":j[l]="",n(j)};return e.jsxs(se,{className:"w-full",children:[e.jsxs(Ne,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ae,{className:"w-5 h-5"}),e.jsx(we,{children:"Filter Posts"}),g()>0&&e.jsxs($,{variant:"secondary",children:[g()," active"]})]}),e.jsx(E,{variant:"ghost",size:"sm",onClick:s,children:e.jsx(I,{className:"w-4 h-4"})})]}),e.jsxs(Se,{className:"space-y-6",children:[g()>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{className:"text-sm font-medium",children:"Active Filters"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[i.type&&e.jsxs($,{variant:"outline",className:"flex items-center gap-1",children:["Type: ",(w=ee.find(l=>l.value===i.type))==null?void 0:w.label,e.jsx("button",{onClick:()=>f("type"),children:e.jsx(I,{className:"w-3 h-3"})})]}),i.organization_id&&e.jsxs($,{variant:"outline",className:"flex items-center gap-1",children:["Org: ",(k=c.find(l=>l.id.toString()===i.organization_id))==null?void 0:k.name,e.jsx("button",{onClick:()=>f("organization_id"),children:e.jsx(I,{className:"w-3 h-3"})})]}),i.campus&&e.jsxs($,{variant:"outline",className:"flex items-center gap-1",children:["Campus: ",i.campus,e.jsx("button",{onClick:()=>f("campus"),children:e.jsx(I,{className:"w-3 h-3"})})]}),i.date_from&&e.jsxs($,{variant:"outline",className:"flex items-center gap-1",children:["From: ",i.date_from,e.jsx("button",{onClick:()=>f("date_from"),children:e.jsx(I,{className:"w-3 h-3"})})]}),i.date_to&&e.jsxs($,{variant:"outline",className:"flex items-center gap-1",children:["To: ",i.date_to,e.jsx("button",{onClick:()=>f("date_to"),children:e.jsx(I,{className:"w-3 h-3"})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"type",children:"Post Type"}),e.jsxs(L,{value:i.type,onValueChange:l=>p("type",l),children:[e.jsx(D,{children:e.jsx(M,{placeholder:"All types"})}),e.jsxs(B,{children:[e.jsx(F,{value:"all",children:"All types"}),ee.map(l=>e.jsx(F,{value:l.value,children:l.label},l.value))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"organization",children:"Organization"}),e.jsxs(L,{value:i.organization_id,onValueChange:l=>p("organization_id",l),children:[e.jsx(D,{children:e.jsx(M,{placeholder:"All organizations"})}),e.jsxs(B,{children:[e.jsx(F,{value:"all",children:"All organizations"}),c.map(l=>e.jsx(F,{value:l.id.toString(),children:l.name},l.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"campus",children:"Campus"}),e.jsxs(L,{value:i.campus,onValueChange:l=>p("campus",l),children:[e.jsx(D,{children:e.jsx(M,{placeholder:"All campuses"})}),e.jsxs(B,{children:[e.jsx(F,{value:"all",children:"All campuses"}),We.map(l=>e.jsx(F,{value:l,children:l},l))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"sort",children:"Sort By"}),e.jsxs(L,{value:i.sort_by,onValueChange:l=>p("sort_by",l),children:[e.jsx(D,{children:e.jsx(M,{})}),e.jsx(B,{children:Ue.map(l=>e.jsx(F,{value:l.value,children:l.label},l.value))})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{children:"Date Range"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx(S,{htmlFor:"date_from",className:"text-xs text-gray-500",children:"From"}),e.jsx(W,{id:"date_from",type:"date",value:i.date_from,onChange:l=>p("date_from",l.target.value)})]}),e.jsxs("div",{children:[e.jsx(S,{htmlFor:"date_to",className:"text-xs text-gray-500",children:"To"}),e.jsx(W,{id:"date_to",type:"date",value:i.date_to,onChange:l=>p("date_to",l.target.value)})]})]})]}),e.jsxs("div",{className:"flex justify-between pt-4 border-t",children:[e.jsxs(E,{variant:"outline",onClick:x,className:"flex items-center gap-2",children:[e.jsx(Me,{className:"w-4 h-4"}),"Reset All"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(E,{variant:"outline",onClick:s,children:"Cancel"}),e.jsx(E,{onClick:m,children:"Apply Filters"})]})]})]})]})}function He(t,o,{checkForDefaultPrevented:s=!0}={}){return function(n){if(t==null||t(n),s===!1||!n.defaultPrevented)return o==null?void 0:o(n)}}function te(t,o){if(typeof t=="function")return t(o);t!=null&&(t.current=o)}function ne(...t){return o=>{let s=!1;const i=t.map(n=>{const c=te(n,o);return!s&&typeof c=="function"&&(s=!0),c});if(s)return()=>{for(let n=0;n<i.length;n++){const c=i[n];typeof c=="function"?c():te(t[n],null)}}}}function re(...t){return a.useCallback(ne(...t),t)}function Xe(t,o=[]){let s=[];function i(c,d){const u=a.createContext(d),h=s.length;s=[...s,d];const p=x=>{var j;const{scope:g,children:f,...w}=x,k=((j=g==null?void 0:g[t])==null?void 0:j[h])||u,l=a.useMemo(()=>w,Object.values(w));return e.jsx(k.Provider,{value:l,children:f})};p.displayName=c+"Provider";function m(x,g){var k;const f=((k=g==null?void 0:g[t])==null?void 0:k[h])||u,w=a.useContext(f);if(w)return w;if(d!==void 0)return d;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[p,m]}const n=()=>{const c=s.map(d=>a.createContext(d));return function(u){const h=(u==null?void 0:u[t])||c;return a.useMemo(()=>({[`__scope${t}`]:{...u,[t]:h}}),[u,h])}};return n.scopeName=t,[i,Ke(n,...o)]}function Ke(...t){const o=t[0];if(t.length===1)return o;const s=()=>{const i=t.map(n=>({useScope:n(),scopeName:n.scopeName}));return function(c){const d=i.reduce((u,{useScope:h,scopeName:p})=>{const x=h(c)[`__scope${p}`];return{...u,...x}},{});return a.useMemo(()=>({[`__scope${o.scopeName}`]:d}),[d])}};return s.scopeName=o.scopeName,s}var ie=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},Qe=be[" useInsertionEffect ".trim().toString()]||ie;function Ye({prop:t,defaultProp:o,onChange:s=()=>{},caller:i}){const[n,c,d]=Ge({defaultProp:o,onChange:s}),u=t!==void 0,h=u?t:n;{const m=a.useRef(t!==void 0);a.useEffect(()=>{const x=m.current;x!==u&&console.warn(`${i} is changing from ${x?"controlled":"uncontrolled"} to ${u?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),m.current=u},[u,i])}const p=a.useCallback(m=>{var x;if(u){const g=Je(m)?m(t):m;g!==t&&((x=d.current)==null||x.call(d,g))}else c(m)},[u,t,c,d]);return[h,p]}function Ge({defaultProp:t,onChange:o}){const[s,i]=a.useState(t),n=a.useRef(s),c=a.useRef(o);return Qe(()=>{c.current=o},[o]),a.useEffect(()=>{var d;n.current!==s&&((d=c.current)==null||d.call(c,s),n.current=s)},[s,n]),[s,i,c]}function Je(t){return typeof t=="function"}function Ze(t){const o=a.useRef({value:t,previous:t});return a.useMemo(()=>(o.current.value!==t&&(o.current.previous=o.current.value,o.current.value=t),o.current.previous),[t])}function et(t){const[o,s]=a.useState(void 0);return ie(()=>{if(t){s({width:t.offsetWidth,height:t.offsetHeight});const i=new ResizeObserver(n=>{if(!Array.isArray(n)||!n.length)return;const c=n[0];let d,u;if("borderBoxSize"in c){const h=c.borderBoxSize,p=Array.isArray(h)?h[0]:h;d=p.inlineSize,u=p.blockSize}else d=t.offsetWidth,u=t.offsetHeight;s({width:d,height:u})});return i.observe(t,{box:"border-box"}),()=>i.unobserve(t)}else s(void 0)},[t]),o}function tt(t){const o=st(t),s=a.forwardRef((i,n)=>{const{children:c,...d}=i,u=a.Children.toArray(c),h=u.find(nt);if(h){const p=h.props.children,m=u.map(x=>x===h?a.Children.count(p)>1?a.Children.only(null):a.isValidElement(p)?p.props.children:null:x);return e.jsx(o,{...d,ref:n,children:a.isValidElement(p)?a.cloneElement(p,void 0,m):null})}return e.jsx(o,{...d,ref:n,children:c})});return s.displayName=`${t}.Slot`,s}function st(t){const o=a.forwardRef((s,i)=>{const{children:n,...c}=s;if(a.isValidElement(n)){const d=it(n),u=rt(c,n.props);return n.type!==a.Fragment&&(u.ref=i?ne(i,d):d),a.cloneElement(n,u)}return a.Children.count(n)>1?a.Children.only(null):null});return o.displayName=`${t}.SlotClone`,o}var at=Symbol("radix.slottable");function nt(t){return a.isValidElement(t)&&typeof t.type=="function"&&"__radixId"in t.type&&t.type.__radixId===at}function rt(t,o){const s={...o};for(const i in o){const n=t[i],c=o[i];/^on[A-Z]/.test(i)?n&&c?s[i]=(...u)=>{const h=c(...u);return n(...u),h}:n&&(s[i]=n):i==="style"?s[i]={...n,...c}:i==="className"&&(s[i]=[n,c].filter(Boolean).join(" "))}return{...t,...s}}function it(t){var i,n;let o=(i=Object.getOwnPropertyDescriptor(t.props,"ref"))==null?void 0:i.get,s=o&&"isReactWarning"in o&&o.isReactWarning;return s?t.ref:(o=(n=Object.getOwnPropertyDescriptor(t,"ref"))==null?void 0:n.get,s=o&&"isReactWarning"in o&&o.isReactWarning,s?t.props.ref:t.props.ref||t.ref)}var ot=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],oe=ot.reduce((t,o)=>{const s=tt(`Primitive.${o}`),i=a.forwardRef((n,c)=>{const{asChild:d,...u}=n,h=d?s:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),e.jsx(h,{...u,ref:c})});return i.displayName=`Primitive.${o}`,{...t,[o]:i}},{}),q="Switch",[lt,It]=Xe(q),[ct,dt]=lt(q),le=a.forwardRef((t,o)=>{const{__scopeSwitch:s,name:i,checked:n,defaultChecked:c,required:d,disabled:u,value:h="on",onCheckedChange:p,form:m,...x}=t,[g,f]=a.useState(null),w=re(o,r=>f(r)),k=a.useRef(!1),l=g?m||!!g.closest("form"):!0,[j,P]=Ye({prop:n,defaultProp:c??!1,onChange:p,caller:q});return e.jsxs(ct,{scope:s,checked:j,disabled:u,children:[e.jsx(oe.button,{type:"button",role:"switch","aria-checked":j,"aria-required":d,"data-state":me(j),"data-disabled":u?"":void 0,disabled:u,value:h,...x,ref:w,onClick:He(t.onClick,r=>{P(b=>!b),l&&(k.current=r.isPropagationStopped(),k.current||r.stopPropagation())})}),l&&e.jsx(ue,{control:g,bubbles:!k.current,name:i,value:h,checked:j,required:d,disabled:u,form:m,style:{transform:"translateX(-100%)"}})]})});le.displayName=q;var ce="SwitchThumb",de=a.forwardRef((t,o)=>{const{__scopeSwitch:s,...i}=t,n=dt(ce,s);return e.jsx(oe.span,{"data-state":me(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:o})});de.displayName=ce;var ut="SwitchBubbleInput",ue=a.forwardRef(({__scopeSwitch:t,control:o,checked:s,bubbles:i=!0,...n},c)=>{const d=a.useRef(null),u=re(d,c),h=Ze(s),p=et(o);return a.useEffect(()=>{const m=d.current;if(!m)return;const x=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(x,"checked").set;if(h!==s&&f){const w=new Event("click",{bubbles:i});f.call(m,s),m.dispatchEvent(w)}},[h,s,i]),e.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:u,style:{...n.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});ue.displayName=ut;function me(t){return t?"checked":"unchecked"}var he=le,mt=de;const Q=a.forwardRef(({className:t,...o},s)=>e.jsx(he,{className:J("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...o,ref:s,children:e.jsx(mt,{className:J("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));Q.displayName=he.displayName;const ht=[{value:"announcement",label:"Announcement",icon:"📢"},{value:"discussion",label:"Discussion",icon:"💬"},{value:"event",label:"Event",icon:"📅"},{value:"news",label:"News",icon:"📰"}],pt=[{value:"public",label:"Public",icon:Re,description:"Anyone can see this post"},{value:"members_only",label:"Members Only",icon:X,description:"Only organization members can see this"},{value:"private",label:"Private",icon:$e,description:"Only you can see this post"}];function xt({onClose:t,onPostCreated:o}){const[s,i]=a.useState({title:"",content:"",type:"announcement",visibility:"public",organization_id:"",is_pinned:!1,comments_enabled:!0,published_at:""}),[n,c]=a.useState([]),[d,u]=a.useState([]),[h,p]=a.useState(!1),[m,x]=a.useState({}),g=a.useRef(null);a.useState(()=>{(async()=>{var b;try{const C=await fetch("/api/v1/organizations?member=true",{headers:{Authorization:`Bearer ${(b=document.querySelector('meta[name="api-token"]'))==null?void 0:b.getAttribute("content")}`,Accept:"application/json"}});if(C.ok){const y=await C.json();u(y.data||[])}}catch(C){console.error("Error loading organizations:",C)}})()});const f=(r,b)=>{i(C=>({...C,[r]:b})),m[r]&&x(C=>({...C,[r]:""}))},w=r=>{const C=Array.from(r.target.files||[]).filter(y=>{const A=y.type.startsWith("image/")||y.type==="application/pdf"||y.type.includes("document"),N=y.size<=10*1024*1024;return A&&N});c(y=>[...y,...C])},k=r=>{c(b=>b.filter((C,y)=>y!==r))},l=()=>{const r={};return s.title.trim()||(r.title="Title is required"),s.content.trim()||(r.content="Content is required"),s.visibility==="members_only"&&!s.organization_id&&(r.organization_id="Organization is required for members-only posts"),x(r),Object.keys(r).length===0},j=async r=>{var b,C;if(r.preventDefault(),!!l()){p(!0);try{const y=new FormData;Object.entries(s).forEach(([N,O])=>{O!==""&&y.append(N,O.toString())}),n.forEach((N,O)=>{y.append(`media[${O}]`,N)});const A=await fetch("/api/v1/posts",{method:"POST",headers:{Authorization:`Bearer ${(b=document.querySelector('meta[name="api-token"]'))==null?void 0:b.getAttribute("content")}`,"X-CSRF-TOKEN":((C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content"))||""},body:y});if(A.ok){const N=await A.json();o(N.post)}else{const N=await A.json();x(N.errors||{general:"Failed to create post"})}}catch(y){console.error("Error creating post:",y),x({general:"Network error. Please try again."})}finally{p(!1)}}},P=d.find(r=>r.id.toString()===s.organization_id);return e.jsx(Pe,{open:!0,onOpenChange:t,children:e.jsxs(Fe,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[e.jsx(Ee,{children:e.jsxs(Ae,{className:"flex items-center gap-2",children:[e.jsx(K,{className:"w-5 h-5"}),"Create New Post"]})}),e.jsxs("form",{onSubmit:j,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{children:"Post Type"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:ht.map(r=>e.jsx("button",{type:"button",onClick:()=>f("type",r.value),className:`p-3 rounded-lg border text-left transition-colors ${s.type===r.value?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-lg",children:r.icon}),e.jsx("span",{className:"font-medium",children:r.label})]})},r.value))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"title",children:"Title *"}),e.jsx(W,{id:"title",value:s.title,onChange:r=>f("title",r.target.value),placeholder:"Enter post title...",className:m.title?"border-red-500":""}),m.title&&e.jsx("p",{className:"text-sm text-red-600",children:m.title})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"content",children:"Content *"}),e.jsx(Oe,{id:"content",value:s.content,onChange:r=>f("content",r.target.value),placeholder:"What's on your mind?",rows:6,className:m.content?"border-red-500":""}),m.content&&e.jsx("p",{className:"text-sm text-red-600",children:m.content})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{children:"Media"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(E,{type:"button",variant:"outline",onClick:()=>{var r;return(r=g.current)==null?void 0:r.click()},className:"w-full",children:[e.jsx(Ve,{className:"w-4 h-4 mr-2"}),"Add Images or Files"]}),e.jsx("input",{ref:g,type:"file",multiple:!0,accept:"image/*,.pdf,.doc,.docx",onChange:w,className:"hidden"}),n.length>0&&e.jsx("div",{className:"grid grid-cols-2 gap-2",children:n.map((r,b)=>e.jsx(se,{className:"p-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[r.type.startsWith("image/")?e.jsx(Le,{className:"w-4 h-4 text-blue-500"}):e.jsx(K,{className:"w-4 h-4 text-gray-500"}),e.jsx("span",{className:"text-sm truncate",children:r.name})]}),e.jsx(E,{type:"button",variant:"ghost",size:"sm",onClick:()=>k(b),children:e.jsx(Te,{className:"w-4 h-4"})})]})},b))})]})]}),d.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{children:"Post as Organization (Optional)"}),e.jsxs(L,{value:s.organization_id,onValueChange:r=>f("organization_id",r),children:[e.jsx(D,{className:m.organization_id?"border-red-500":"",children:e.jsx(M,{placeholder:"Select organization..."})}),e.jsxs(B,{children:[e.jsx(F,{value:"personal",children:"Personal Post"}),d.map(r=>e.jsx(F,{value:r.id.toString(),children:r.name},r.id))]})]}),m.organization_id&&e.jsx("p",{className:"text-sm text-red-600",children:m.organization_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{children:"Visibility"}),e.jsx("div",{className:"space-y-2",children:pt.map(r=>{const b=r.icon;return e.jsx("button",{type:"button",onClick:()=>f("visibility",r.value),disabled:r.value==="members_only"&&!s.organization_id,className:`w-full p-3 rounded-lg border text-left transition-colors ${s.visibility===r.value?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${r.value==="members_only"&&!s.organization_id?"opacity-50 cursor-not-allowed":""}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(b,{className:"w-5 h-5"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:r.label}),e.jsx("div",{className:"text-sm text-gray-500",children:r.description})]})]})},r.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(S,{htmlFor:"comments_enabled",children:"Enable Comments"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Allow users to comment on this post"})]}),e.jsx(Q,{id:"comments_enabled",checked:s.comments_enabled,onCheckedChange:r=>f("comments_enabled",r)})]}),P&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(S,{htmlFor:"is_pinned",children:"Pin Post"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Pin this post to the top of the feed"})]}),e.jsx(Q,{id:"is_pinned",checked:s.is_pinned,onCheckedChange:r=>f("is_pinned",r)})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(S,{htmlFor:"published_at",children:"Schedule Post (Optional)"}),e.jsx(W,{id:"published_at",type:"datetime-local",value:s.published_at,onChange:r=>f("published_at",r.target.value),min:new Date().toISOString().slice(0,16)}),e.jsx("p",{className:"text-sm text-gray-500",children:"Leave empty to publish immediately"})]}),m.general&&e.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-600",children:m.general})}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4 border-t",children:[e.jsx(E,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),e.jsx(E,{type:"submit",disabled:h,children:h?"Creating...":"Create Post"})]})]})]})})}const ft=[{title:"Feed",href:"/feed"}];function Lt({initialPosts:t,feedType:o="personalized"}){var G;const{props:s}=ve(),i=(G=s.auth)==null?void 0:G.api_token,[n,c]=a.useState((t==null?void 0:t.data)||[]),[d,u]=a.useState(!1),[h,p]=a.useState(!1),[m,x]=a.useState(t?t.current_page<t.last_page:!0),[g,f]=a.useState((t==null?void 0:t.current_page)||1),[w,k]=a.useState(""),[l,j]=a.useState(!1),[P,r]=a.useState(!1),[b,C]=a.useState(o);console.log("Feed component props:",{initialPosts:t,feedType:o,posts:n});const[y,A]=a.useState({type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"}),N=a.useCallback(async(v=1,z=!1)=>{u(!0);try{const T=new URLSearchParams({page:v.toString(),per_page:"15",...w&&{search:w},...Object.fromEntries(Object.entries(y).filter(([_,H])=>H!==""))});let R="/api/v1/posts";b==="personalized"?R="/api/v1/feed":b==="trending"&&(R="/api/v1/feed/trending");const V=await fetch(`${R}?${T}`,{headers:{Authorization:`Bearer ${i}`,Accept:"application/json"}});if(V.ok){const _=await V.json();c(z?H=>[...H,..._.data]:_.data),f(_.current_page),x(_.current_page<_.last_page)}}catch(T){console.error("Error loading posts:",T)}finally{u(!1)}},[b,w,y]),O=a.useCallback(()=>{!d&&m&&N(g+1,!0)},[d,m,g,N]);a.useCallback(async()=>{p(!0),await N(1,!1),p(!1)},[N]),a.useCallback(v=>{v.preventDefault(),N(1,!1)},[N]);const pe=a.useCallback(v=>{A(v),N(1,!1)},[N]),xe=a.useCallback(v=>{C(v),f(1),c([])},[]);a.useEffect(()=>{N(1,!1)},[b]),a.useEffect(()=>{const v=()=>{window.innerHeight+document.documentElement.scrollTop!==document.documentElement.offsetHeight||d||O()};return window.addEventListener("scroll",v),()=>window.removeEventListener("scroll",v)},[O,d]);const fe=async(v,z)=>{var T;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`,"X-CSRF-TOKEN":((T=document.querySelector('meta[name="csrf-token"]'))==null?void 0:T.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:v,type:z})})).ok&&c(V=>V.map(_=>_.id===v?{..._,reactions_count:_.user_reaction===z?_.reactions_count-1:_.reactions_count+(_.user_reaction?0:1),user_reaction:_.user_reaction===z?void 0:z}:_))}catch(R){console.error("Error toggling reaction:",R)}},ge=v=>{window.location.href=`/posts/${v}#comments`},je=v=>{var z;(z=navigator.share)==null||z.call(navigator,{title:"UniLink Post",url:`${window.location.origin}/posts/${v}`})};return e.jsxs(Ce,{breadcrumbs:ft,children:[e.jsx(ye,{title:"Feed"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-md-3 mb-4",children:e.jsx("div",{className:"sidebar-content",children:e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header bg-unilink-primary text-white",children:e.jsx("h5",{className:"mb-0",children:"Menu"})}),e.jsxs("div",{className:"list-group list-group-flush",children:[e.jsxs(U,{href:"/profile",className:"list-group-item list-group-item-action",children:[e.jsx(X,{className:"w-4 h-4 me-2"}),"Profile"]}),e.jsxs(U,{href:"/organizations",className:"list-group-item list-group-item-action",children:[e.jsx(X,{className:"w-4 h-4 me-2"}),"My Organizations"]}),e.jsxs(U,{href:"/events",className:"list-group-item list-group-item-action",children:[e.jsx(_e,{className:"w-4 h-4 me-2"}),"Events"]}),e.jsxs(U,{href:"/settings",className:"list-group-item list-group-item-action",children:[e.jsx(ke,{className:"w-4 h-4 me-2"}),"Settings"]})]})]})})}),e.jsxs("div",{className:"col-md-6 mb-4",children:[e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("button",{onClick:()=>xe("personalized"),className:`btn btn-sm me-2 ${b==="personalized"?"btn-primary":"btn-outline-primary"}`,children:"All"}),e.jsxs("button",{onClick:()=>j(!l),className:"btn btn-outline-primary btn-sm",children:[e.jsx(ae,{className:"w-4 h-4 me-1"}),"Filter"]})]}),e.jsxs("button",{onClick:()=>r(!0),className:"btn btn-primary btn-sm",children:[e.jsx(Z,{className:"w-4 h-4 me-1"}),"Create Post"]})]})})}),l&&e.jsx(qe,{filters:y,onFiltersChange:pe,onClose:()=>j(!1)}),e.jsxs("div",{className:"feed-content",children:[n.length>0?n.map(v=>e.jsx(ze,{post:v,onReact:fe,onComment:ge,onShare:je},v.id)):d?null:e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx(K,{className:"w-12 h-12 text-muted mx-auto mb-4"}),e.jsx("h3",{className:"h5 mb-2",children:"No posts found"}),e.jsx("p",{className:"text-muted mb-4",children:"Be the first to add a post!"}),e.jsxs("button",{onClick:()=>r(!0),className:"btn btn-primary",children:[e.jsx(Z,{className:"w-4 h-4 me-2"}),"Create Post"]})]})}),d&&e.jsx("div",{children:[...Array(3)].map((v,z)=>e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx("div",{className:"placeholder-glow",children:e.jsx("div",{className:"placeholder rounded-circle me-3",style:{width:"40px",height:"40px"}})}),e.jsxs("div",{className:"placeholder-glow flex-grow-1",children:[e.jsx("div",{className:"placeholder col-4 mb-2"}),e.jsx("div",{className:"placeholder col-3"})]})]}),e.jsxs("div",{className:"placeholder-glow",children:[e.jsx("div",{className:"placeholder col-8 mb-3"}),e.jsx("div",{className:"placeholder col-12 mb-3",style:{height:"80px"}}),e.jsxs("div",{className:"d-flex",children:[e.jsx("div",{className:"placeholder col-2 me-2"}),e.jsx("div",{className:"placeholder col-2 me-2"}),e.jsx("div",{className:"placeholder col-2"})]})]})]})},z))})]})]}),e.jsx("div",{className:"col-md-3 mb-4",children:e.jsxs("div",{children:[e.jsxs("div",{className:"card shadow-sm mb-4",children:[e.jsx("div",{className:"card-header bg-unilink-primary text-white",children:e.jsx("h5",{className:"mb-0",children:"Upcoming Events"})}),e.jsxs("div",{className:"card-body",children:[e.jsx("div",{className:"small text-muted mb-2",children:"Enrollment Period: Aug 1-15"}),e.jsx("div",{className:"small text-muted mb-2",children:"Orientation Day: Aug 20"}),e.jsx("div",{className:"small text-muted mb-2",children:"First Day of Classes: Aug 22"}),e.jsx("div",{className:"small text-muted",children:"Foundation Day: Sept 15"})]})]}),e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header bg-unilink-primary text-white",children:e.jsx("h5",{className:"mb-0",children:"Quick Links"})}),e.jsxs("div",{className:"list-group list-group-flush",children:[e.jsx("a",{href:"https://sksu.edu.ph/",target:"_blank",rel:"noopener noreferrer",className:"list-group-item list-group-item-action",children:"University Website"}),e.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Student Portal"}),e.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Library Resources"}),e.jsx("a",{href:"#",className:"list-group-item list-group-item-action",children:"Academic Calendar"})]})]})]})})]})})})}),P&&e.jsx(xt,{onClose:()=>r(!1),onPostCreated:v=>{c(z=>[v,...z]),r(!1)}})]})}export{Lt as default};
