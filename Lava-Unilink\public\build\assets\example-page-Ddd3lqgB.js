import{j as e,L as a}from"./app-BYdmc_gi.js";import{A as s}from"./app-layout-B0AAWw2-.js";import{T as r}from"./three-column-layout-CNpB7Avz.js";import"./app-logo-icon-D4PatI0y.js";import"./index-CutG6Jxx.js";import"./index-CGyr8X69.js";import"./book-open-CocWnqgv.js";import"./message-circle-C-hErto7.js";const t=[{title:"Example Page",href:"/example"}];function h(){return e.jsxs(s,{breadcrumbs:t,children:[e.jsx(a,{title:"Example Page"}),e.jsx(r,{children:e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsx("h4",{className:"mb-0",children:"Example with Default Sidebars"})}),e.jsxs("div",{className:"card-body",children:[e.jsx("p",{children:"This page uses the default left and right sidebars from the ThreeColumnLayout component."}),e.jsx("p",{children:"The layout automatically provides consistent navigation and quick links."})]})]})})]})}export{h as default};
