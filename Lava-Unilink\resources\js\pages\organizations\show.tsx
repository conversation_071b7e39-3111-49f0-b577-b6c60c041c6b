import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    ArrowLeft,
    Users,
    Calendar,
    MapPin,
    Globe,
    Mail,
    Phone,
    UserPlus,
    Settings,
    Edit,
    MessageCircle,
    BookOpen,
    Star
} from 'lucide-react';

interface Organization {
    id: number;
    name: string;
    description: string;
    type: string;
    logo?: string;
    banner?: string;
    website?: string;
    email?: string;
    phone?: string;
    location?: string;
    campus: string;
    established_date?: string;
    members_count: number;
    posts_count: number;
    events_count: number;
    is_member: boolean;
    is_admin: boolean;
    membership_status?: 'pending' | 'approved' | 'rejected';
    created_at: string;
}

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
}

interface OrganizationShowProps {
    organizationId: string;
    organization?: Organization;
    posts?: Post[];
}

export default function OrganizationShow({ organizationId, organization: initialOrganization, posts: initialPosts }: OrganizationShowProps) {
    const [organization, setOrganization] = useState<Organization | null>(initialOrganization || null);
    const [posts, setPosts] = useState<Post[]>(initialPosts || []);
    const [loading, setLoading] = useState(!initialOrganization);
    const [postsLoading, setPostsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState('posts');

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Organizations',
            href: '/organizations',
        },
        {
            title: organization?.name || 'Organization',
            href: `/organizations/${organizationId}`,
        },
    ];

    // Load organization if not provided
    useEffect(() => {
        if (!initialOrganization) {
            loadOrganization();
        }
        if (!initialPosts) {
            loadOrganizationPosts();
        }
    }, [organizationId]);

    const loadOrganization = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await fetch(`/api/v1/organizations/${organizationId}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setOrganization(data);
            } else if (response.status === 404) {
                setError('Organization not found');
            } else {
                setError('Failed to load organization');
            }
        } catch (error) {
            console.error('Error loading organization:', error);
            setError('An unexpected error occurred');
        } finally {
            setLoading(false);
        }
    };

    const loadOrganizationPosts = async () => {
        setPostsLoading(true);
        try {
            const response = await fetch(`/api/v1/organizations/${organizationId}/posts`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setPosts(data.data || []);
            }
        } catch (error) {
            console.error('Error loading organization posts:', error);
        } finally {
            setPostsLoading(false);
        }
    };

    const handleJoin = async () => {
        if (!organization) return;

        try {
            const response = await fetch(`/api/v1/organizations/${organization.id}/join`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setOrganization(prev => prev ? {
                    ...prev,
                    is_member: data.is_member,
                    membership_status: data.membership_status,
                    members_count: data.is_member ? prev.members_count + 1 : prev.members_count
                } : null);
            } else {
                alert('Failed to join organization');
            }
        } catch (error) {
            console.error('Error joining organization:', error);
            alert('An error occurred while joining');
        }
    };

    const handleLeave = async () => {
        if (!organization || !confirm('Are you sure you want to leave this organization?')) return;

        try {
            const response = await fetch(`/api/v1/organizations/${organization.id}/leave`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                setOrganization(prev => prev ? {
                    ...prev,
                    is_member: false,
                    membership_status: undefined,
                    members_count: prev.members_count - 1
                } : null);
            } else {
                alert('Failed to leave organization');
            }
        } catch (error) {
            console.error('Error leaving organization:', error);
            alert('An error occurred while leaving');
        }
    };

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { 
                                ...post, 
                                reactions_count: post.user_reaction === reactionType 
                                    ? post.reactions_count - 1 
                                    : post.reactions_count + (post.user_reaction ? 0 : 1),
                                user_reaction: post.user_reaction === reactionType ? undefined : reactionType
                            }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        navigator.share?.({
            title: 'UniLink Post',
            url: `${window.location.origin}/posts/${postId}`
        });
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'academic': return 'bg-primary';
            case 'cultural': return 'bg-success';
            case 'sports': return 'bg-warning';
            case 'religious': return 'bg-info';
            case 'service': return 'bg-secondary';
            default: return 'bg-primary';
        }
    };

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Loading..." />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="text-center py-5">
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <p className="mt-3 text-muted">Loading organization...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (error || !organization) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Error" />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="card shadow-sm">
                                <div className="card-body text-center py-5">
                                    <h3 className="h5 mb-3">Organization Not Found</h3>
                                    <p className="text-muted mb-4">
                                        {error || 'The organization you are looking for does not exist.'}
                                    </p>
                                    <Link href="/organizations" className="btn btn-primary">
                                        <ArrowLeft className="w-4 h-4 me-2" />
                                        Back to Organizations
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={organization.name} />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        {/* Header */}
                        <div className="d-flex align-items-center justify-content-between mb-4">
                            <Link href="/organizations" className="btn btn-outline-secondary">
                                <ArrowLeft className="w-4 h-4 me-2" />
                                Back to Organizations
                            </Link>
                            
                            {organization.is_admin && (
                                <div className="d-flex gap-2">
                                    <Link href={`/organizations/${organization.id}/edit`} className="btn btn-outline-primary">
                                        <Edit className="w-4 h-4 me-2" />
                                        Edit
                                    </Link>
                                    <Link href={`/organizations/${organization.id}/manage`} className="btn btn-outline-secondary">
                                        <Settings className="w-4 h-4 me-2" />
                                        Manage
                                    </Link>
                                </div>
                            )}
                        </div>

                        {/* Organization Header */}
                        <div className="card shadow-sm mb-4">
                            {organization.banner && (
                                <img
                                    src={`/storage/${organization.banner}`}
                                    alt={organization.name}
                                    className="card-img-top"
                                    style={{height: '200px', objectFit: 'cover'}}
                                />
                            )}
                            <div className="card-body">
                                <div className="row align-items-center">
                                    <div className="col-md-2 text-center">
                                        <img
                                            src={organization.logo ? `/storage/${organization.logo}` : '/img/org-placeholder.png'}
                                            alt={organization.name}
                                            className="rounded mb-3"
                                            width="80"
                                            height="80"
                                            style={{objectFit: 'cover'}}
                                        />
                                    </div>
                                    <div className="col-md-7">
                                        <h1 className="h3 mb-2">{organization.name}</h1>
                                        <div className="d-flex align-items-center mb-2">
                                            <span className={`badge ${getTypeColor(organization.type)} me-2`}>
                                                {organization.type}
                                            </span>
                                            <span className="text-muted">{organization.campus}</span>
                                        </div>
                                        <p className="text-muted mb-3">{organization.description}</p>
                                        
                                        <div className="row g-2 text-muted small">
                                            {organization.location && (
                                                <div className="col-md-6">
                                                    <MapPin className="w-4 h-4 me-1" />
                                                    {organization.location}
                                                </div>
                                            )}
                                            {organization.established_date && (
                                                <div className="col-md-6">
                                                    <Calendar className="w-4 h-4 me-1" />
                                                    Est. {new Date(organization.established_date).getFullYear()}
                                                </div>
                                            )}
                                            {organization.website && (
                                                <div className="col-md-6">
                                                    <Globe className="w-4 h-4 me-1" />
                                                    <a href={organization.website} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                                                        Website
                                                    </a>
                                                </div>
                                            )}
                                            {organization.email && (
                                                <div className="col-md-6">
                                                    <Mail className="w-4 h-4 me-1" />
                                                    <a href={`mailto:${organization.email}`} className="text-decoration-none">
                                                        {organization.email}
                                                    </a>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="col-md-3 text-center">
                                        {organization.is_member ? (
                                            <div className="d-flex flex-column gap-2">
                                                <span className="badge bg-success mb-2">Member</span>
                                                <button onClick={handleLeave} className="btn btn-outline-danger btn-sm">
                                                    Leave Organization
                                                </button>
                                                <Link href={`/organizations/${organization.id}/discussions`} className="btn btn-outline-primary btn-sm">
                                                    <MessageCircle className="w-4 h-4 me-1" />
                                                    Discussions
                                                </Link>
                                            </div>
                                        ) : organization.membership_status === 'pending' ? (
                                            <div>
                                                <span className="badge bg-warning mb-2">Pending</span>
                                                <p className="small text-muted">Your membership request is being reviewed</p>
                                            </div>
                                        ) : (
                                            <button onClick={handleJoin} className="btn btn-primary">
                                                <UserPlus className="w-4 h-4 me-2" />
                                                Join Organization
                                            </button>
                                        )}
                                    </div>
                                </div>

                                {/* Stats */}
                                <div className="row text-center mt-4 pt-3 border-top">
                                    <div className="col-4">
                                        <div className="h5 mb-0">{organization.members_count}</div>
                                        <small className="text-muted">Members</small>
                                    </div>
                                    <div className="col-4">
                                        <div className="h5 mb-0">{organization.posts_count}</div>
                                        <small className="text-muted">Posts</small>
                                    </div>
                                    <div className="col-4">
                                        <div className="h5 mb-0">{organization.events_count}</div>
                                        <small className="text-muted">Events</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Content Tabs */}
                        <div className="card shadow-sm">
                            <div className="card-header">
                                <ul className="nav nav-tabs card-header-tabs">
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'posts' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('posts')}
                                        >
                                            Posts ({organization.posts_count})
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'events' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('events')}
                                        >
                                            Events ({organization.events_count})
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'members' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('members')}
                                        >
                                            Members ({organization.members_count})
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'about' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('about')}
                                        >
                                            About
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div className="card-body">
                                {activeTab === 'posts' && (
                                    <div>
                                        {postsLoading ? (
                                            <div className="text-center py-4">
                                                <div className="spinner-border text-primary" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                        ) : posts.length > 0 ? (
                                            <div className="space-y-4">
                                                {posts.map((post) => (
                                                    <PostCard
                                                        key={post.id}
                                                        post={post}
                                                        onReact={handleReaction}
                                                        onComment={handleComment}
                                                        onShare={handleShare}
                                                    />
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="text-center py-5">
                                                <BookOpen className="w-12 h-12 text-muted mx-auto mb-3" />
                                                <h5 className="mb-2">No posts yet</h5>
                                                <p className="text-muted">This organization hasn't posted anything yet.</p>
                                            </div>
                                        )}
                                    </div>
                                )}

                                {activeTab === 'events' && (
                                    <div className="text-center py-5">
                                        <Calendar className="w-12 h-12 text-muted mx-auto mb-3" />
                                        <h5 className="mb-2">Events</h5>
                                        <p className="text-muted">Organization events will be shown here.</p>
                                    </div>
                                )}

                                {activeTab === 'members' && (
                                    <div className="text-center py-5">
                                        <Users className="w-12 h-12 text-muted mx-auto mb-3" />
                                        <h5 className="mb-2">Members</h5>
                                        <p className="text-muted">Organization members will be shown here.</p>
                                    </div>
                                )}

                                {activeTab === 'about' && (
                                    <div>
                                        <h5 className="mb-3">About {organization.name}</h5>
                                        <p className="text-muted mb-4">{organization.description}</p>
                                        
                                        <div className="row">
                                            <div className="col-md-6">
                                                <h6>Organization Details</h6>
                                                <ul className="list-unstyled">
                                                    <li><strong>Type:</strong> {organization.type}</li>
                                                    <li><strong>Campus:</strong> {organization.campus}</li>
                                                    {organization.established_date && (
                                                        <li><strong>Established:</strong> {new Date(organization.established_date).toLocaleDateString()}</li>
                                                    )}
                                                    {organization.location && (
                                                        <li><strong>Location:</strong> {organization.location}</li>
                                                    )}
                                                </ul>
                                            </div>
                                            <div className="col-md-6">
                                                <h6>Contact Information</h6>
                                                <ul className="list-unstyled">
                                                    {organization.email && (
                                                        <li><strong>Email:</strong> <a href={`mailto:${organization.email}`}>{organization.email}</a></li>
                                                    )}
                                                    {organization.phone && (
                                                        <li><strong>Phone:</strong> {organization.phone}</li>
                                                    )}
                                                    {organization.website && (
                                                        <li><strong>Website:</strong> <a href={organization.website} target="_blank" rel="noopener noreferrer">{organization.website}</a></li>
                                                    )}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
