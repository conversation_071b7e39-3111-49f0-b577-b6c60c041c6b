import{r as m,j as e,L as k,S as x}from"./app-2kjRWvs5.js";import{A as S,f as E,X as A,U as F}from"./app-layout-Susg8ZLQ.js";import{A as O}from"./arrow-left-B0ljqPJU.js";import{E as j}from"./eye-DXFSAmYI.js";import{L as P}from"./lock-6QWOr0qc.js";import"./app-logo-icon-ylztpkIq.js";import"./index-8HzOgwF5.js";import"./index-CdTsfyPs.js";const L=[{title:"Posts",href:"/posts"},{title:"Create Post",href:"/posts/create"}];function X(){const[t,h]=m.useState({title:"",content:"",type:"discussion",visibility:"public",organization_id:"",comments_enabled:!0,is_pinned:!1,media:[]}),[v,f]=m.useState([]),[b,p]=m.useState(!1),[n,d]=m.useState({}),[D,$]=m.useState(!1);m.useEffect(()=>{(async()=>{var a;try{const i=await fetch("/api/v1/organizations/my",{headers:{Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,Accept:"application/json"}});if(i.ok){const l=await i.json();f(l.data||[])}}catch(i){console.error("Error loading organizations:",i)}})()},[]);const o=s=>{const{name:a,value:i,type:l}=s.target;h(r=>({...r,[a]:l==="checkbox"?s.target.checked:i})),n[a]&&d(r=>({...r,[a]:""}))},g=s=>{const a=Array.from(s.target.files||[]);h(i=>({...i,media:[...i.media,...a].slice(0,5)}))},N=s=>{h(a=>({...a,media:a.media.filter((i,l)=>l!==s)}))},y=()=>{const s={};return t.title.trim()?t.title.length>255&&(s.title="Title must be less than 255 characters"):s.title="Title is required",t.content.trim()?t.content.length>1e4&&(s.content="Content must be less than 10,000 characters"):s.content="Content is required",t.visibility==="members_only"&&!t.organization_id&&(s.organization_id="Organization is required for members-only posts"),d(s),Object.keys(s).length===0},w=async s=>{var a,i;if(s.preventDefault(),!!y()){p(!0);try{const l=new FormData;Object.entries(t).forEach(([c,u])=>{c==="media"?u.forEach((z,_)=>{l.append(`media[${_}]`,z)}):l.append(c,u.toString())});const r=await fetch("/api/v1/posts",{method:"POST",headers:{Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,"X-CSRF-TOKEN":((i=document.querySelector('meta[name="csrf-token"]'))==null?void 0:i.getAttribute("content"))||""},body:l});if(r.ok){const c=await r.json();x.visit(`/posts/${c.id}`,{onSuccess:()=>{}})}else{const c=await r.json();d(c.errors||{general:"Failed to create post"})}}catch(l){console.error("Error creating post:",l),d({general:"An unexpected error occurred"})}finally{p(!1)}}},C=s=>{switch(s){case"public":return e.jsx(j,{className:"w-4 h-4"});case"members_only":return e.jsx(F,{className:"w-4 h-4"});case"private":return e.jsx(P,{className:"w-4 h-4"});default:return e.jsx(j,{className:"w-4 h-4"})}};return e.jsxs(S,{breadcrumbs:L,children:[e.jsx(k,{title:"Create Post"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-md-8 mx-auto",children:[e.jsxs("div",{className:"d-flex align-items-center mb-4",children:[e.jsx("button",{onClick:()=>x.visit("/posts"),className:"btn btn-outline-secondary me-3",children:e.jsx(O,{className:"w-4 h-4"})}),e.jsx("h1",{className:"h3 mb-0",children:"Create New Post"})]}),e.jsx("form",{onSubmit:w,children:e.jsxs("div",{className:"card shadow-sm",children:[e.jsxs("div",{className:"card-body",children:[n.general&&e.jsxs("div",{className:"alert alert-danger d-flex align-items-center mb-4",children:[e.jsx(E,{className:"w-4 h-4 me-2"}),n.general]}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("label",{htmlFor:"title",className:"form-label",children:["Title ",e.jsx("span",{className:"text-danger",children:"*"})]}),e.jsx("input",{type:"text",id:"title",name:"title",className:`form-control ${n.title?"is-invalid":""}`,value:t.title,onChange:o,placeholder:"Enter post title...",maxLength:255}),n.title&&e.jsx("div",{className:"invalid-feedback",children:n.title})]}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("label",{htmlFor:"content",className:"form-label",children:["Content ",e.jsx("span",{className:"text-danger",children:"*"})]}),e.jsx("textarea",{id:"content",name:"content",className:`form-control ${n.content?"is-invalid":""}`,rows:8,value:t.content,onChange:o,placeholder:"Write your post content...",maxLength:1e4}),n.content&&e.jsx("div",{className:"invalid-feedback",children:n.content}),e.jsxs("div",{className:"form-text",children:[t.content.length,"/10,000 characters"]})]}),e.jsxs("div",{className:"row mb-3",children:[e.jsxs("div",{className:"col-md-6",children:[e.jsx("label",{htmlFor:"type",className:"form-label",children:"Post Type"}),e.jsxs("select",{id:"type",name:"type",className:"form-select",value:t.type,onChange:o,children:[e.jsx("option",{value:"discussion",children:"Discussion"}),e.jsx("option",{value:"announcement",children:"Announcement"}),e.jsx("option",{value:"event",children:"Event"}),e.jsx("option",{value:"news",children:"News"})]})]}),e.jsxs("div",{className:"col-md-6",children:[e.jsx("label",{htmlFor:"visibility",className:"form-label",children:"Visibility"}),e.jsxs("select",{id:"visibility",name:"visibility",className:"form-select",value:t.visibility,onChange:o,children:[e.jsx("option",{value:"public",children:"Public"}),e.jsx("option",{value:"members_only",children:"Members Only"}),e.jsx("option",{value:"private",children:"Private"})]})]})]}),t.visibility==="members_only"&&e.jsxs("div",{className:"mb-3",children:[e.jsxs("label",{htmlFor:"organization_id",className:"form-label",children:["Organization ",e.jsx("span",{className:"text-danger",children:"*"})]}),e.jsxs("select",{id:"organization_id",name:"organization_id",className:`form-select ${n.organization_id?"is-invalid":""}`,value:t.organization_id,onChange:o,children:[e.jsx("option",{value:"",children:"Select an organization"}),v.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),n.organization_id&&e.jsx("div",{className:"invalid-feedback",children:n.organization_id})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("label",{className:"form-label",children:"Media (Optional)"}),e.jsxs("div",{className:"border rounded p-3",children:[e.jsx("input",{type:"file",multiple:!0,accept:"image/*,video/*,.pdf,.doc,.docx",onChange:g,className:"form-control mb-2",disabled:t.media.length>=5}),e.jsx("small",{className:"text-muted",children:"Upload up to 5 files (images, videos, documents)"}),t.media.length>0&&e.jsx("div",{className:"mt-3",children:t.media.map((s,a)=>e.jsxs("div",{className:"d-flex align-items-center justify-content-between bg-light p-2 rounded mb-2",children:[e.jsx("span",{className:"small",children:s.name}),e.jsx("button",{type:"button",onClick:()=>N(a),className:"btn btn-sm btn-outline-danger",children:e.jsx(A,{className:"w-3 h-3"})})]},a))})]})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"comments_enabled",name:"comments_enabled",className:"form-check-input",checked:t.comments_enabled,onChange:o}),e.jsx("label",{htmlFor:"comments_enabled",className:"form-check-label",children:"Enable comments"})]})})]}),e.jsx("div",{className:"card-footer bg-light",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex align-items-center text-muted small",children:[C(t.visibility),e.jsxs("span",{className:"ms-1",children:[t.visibility==="public"&&"Everyone can see this post",t.visibility==="members_only"&&"Only organization members can see this post",t.visibility==="private"&&"Only you can see this post"]})]}),e.jsxs("div",{className:"d-flex gap-2",children:[e.jsx("button",{type:"button",onClick:()=>x.visit("/posts"),className:"btn btn-outline-secondary",disabled:b,children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:b,children:b?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"spinner-border spinner-border-sm me-2"}),"Creating..."]}):"Create Post"})]})]})})]})})]})})})})})]})}export{X as default};
