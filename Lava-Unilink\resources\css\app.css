@tailwind base;
@tailwind components;
@tailwind utilities;

/* UniLink Color Variables - matching prototype */
:root {
    --color-lightest: #EEEEEE; /* Light Gray */
    --color-third-darkest: #7BC74D; /* Green */
    --color-second-darkest: #393E46; /* Dark Gray */
    --color-darkest: #222831; /* Dark Navy */

    /* Tailwind CSS variables */
    --background: 238 238 238; /* #EEEEEE */
    --foreground: 34 40 49; /* #222831 */

    --primary: 123 199 77; /* #7BC74D */
    --primary-foreground: 255 255 255;

    --secondary: 57 62 70; /* #393E46 */
    --secondary-foreground: 238 238 238;

    --muted: 220 10% 90%;
    --muted-foreground: 220 10% 40%;

    --accent: 123 199 77; /* #7BC74D */
    --accent-foreground: 255 255 255;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 10% 85%;
    --input: 220 10% 85%;
    --ring: 123 199 77; /* #7BC74D */

    --radius: 0.5rem;

    /* Sidebar variables */
    --sidebar-background: 238 238 238; /* #EEEEEE */
    --sidebar-foreground: 34 40 49; /* #222831 */
    --sidebar-primary: 123 199 77; /* #7BC74D */
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 123 199 77; /* #7BC74D */
    --sidebar-accent-foreground: 255 255 255;
    --sidebar-border: 220 10% 85%;
    --sidebar-ring: 123 199 77; /* #7BC74D */

    /* Card variables */
    --card: 255 255 255;
    --card-foreground: 34 40 49; /* #222831 */
}

  .dark {
    --color-lightest: #222831; /* Dark Navy for dark mode background */
    --color-third-darkest: #7BC74D; /* Green stays the same */
    --color-second-darkest: #EEEEEE; /* Light Gray for dark mode text */
    --color-darkest: #EEEEEE; /* Light Gray for dark mode text */

    --background: 34 40 49; /* #222831 */
    --foreground: 238 238 238; /* #EEEEEE */

    --primary: 123 199 77; /* #7BC74D */
    --primary-foreground: 34 40 49; /* #222831 */

    --secondary: 57 62 70; /* #393E46 */
    --secondary-foreground: 238 238 238; /* #EEEEEE */

    --muted: 57 62 70; /* #393E46 */
    --muted-foreground: 238 238 238; /* #EEEEEE */

    --accent: 123 199 77; /* #7BC74D */
    --accent-foreground: 34 40 49; /* #222831 */

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 57 62 70; /* #393E46 */
    --input: 57 62 70; /* #393E46 */
    --ring: 123 199 77; /* #7BC74D */

    /* Sidebar variables for dark mode */
    --sidebar-background: 34 40 49; /* #222831 */
    --sidebar-foreground: 238 238 238; /* #EEEEEE */
    --sidebar-primary: 123 199 77; /* #7BC74D */
    --sidebar-primary-foreground: 34 40 49; /* #222831 */
    --sidebar-accent: 123 199 77; /* #7BC74D */
    --sidebar-accent-foreground: 34 40 49; /* #222831 */
    --sidebar-border: 57 62 70; /* #393E46 */
    --sidebar-ring: 123 199 77; /* #7BC74D */

    /* Card variables for dark mode */
    --card: 57 62 70; /* #393E46 */
    --card-foreground: 238 238 238; /* #EEEEEE */
  }


/* General styles matching prototype */
body {
    background-color: var(--color-lightest);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--color-darkest);
}

/* Button styling matching prototype */
.btn-primary {
    background-color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    color: white !important;
}

.btn-primary:hover {
    background-color: #6AB33C !important;
    border-color: #6AB33C !important;
    color: white !important;
}

.btn-outline-primary {
    color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--color-third-darkest) !important;
    color: white !important;
    border-color: var(--color-third-darkest) !important;
}

/* Card styling matching prototype */
.card {
    background-color: white !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    color: var(--color-darkest) !important;
}

.card-body {
    background-color: white !important;
    color: var(--color-darkest) !important;
}

.card-title {
    color: var(--color-darkest) !important;
}

.card-text {
    color: var(--color-darkest) !important;
}

.card-header.bg-primary {
    background-color: var(--color-third-darkest) !important;
    color: white !important;
}

/* Post styling matching prototype */
.post {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

/* Feed content adjustments matching prototype */
.feed-content {
    max-width: 100%;
    width: 100%;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    padding: 0;
}

.feed-content .card {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1.5rem;
}

/* Main container matching prototype */
.main-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.content-area {
    flex: 1;
    overflow: hidden;
    position: relative;
    padding: 20px 0;
}

/* Feed layout improvements */
.feed-layout-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Ensure proper spacing between columns */
.row.g-4 {
    --bs-gutter-x: 2rem;
    --bs-gutter-y: 2rem;
}

/* Responsive adjustments for feed */
@media (max-width: 1199.98px) {
    .feed-layout-container {
        max-width: 100%;
        padding: 0 15px;
    }
}

@media (max-width: 991.98px) {
    .feed-content {
        padding: 0;
        max-height: calc(100vh - 150px);
    }

    .row.g-4 {
        --bs-gutter-x: 1.5rem;
        --bs-gutter-y: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .content-area {
        padding: 10px 0;
    }

    .feed-content {
        max-height: calc(100vh - 120px);
    }

    .feed-layout-container {
        padding: 0 10px;
    }

    .row.g-4 {
        --bs-gutter-x: 1rem;
        --bs-gutter-y: 1rem;
    }
}

/* Sidebar scrolling matching prototype */
.sidebar-content {
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
}

/* Bootstrap-style classes with UniLink colors */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
}

.col-md-3, .col-md-6 {
    position: relative;
    width: 100%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

@media (min-width: 768px) {
    .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

.list-group-flush {
    border-radius: 0;
}

.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0;
}

.list-group-flush .list-group-item:first-child {
    border-top: 0;
}

.list-group-flush .list-group-item:last-child {
    border-bottom: 0;
}

.list-group-item {
    position: relative;
    display: block;
    padding: 0.75rem 1.25rem;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
    text-decoration: none;
    color: var(--color-darkest);
}

.list-group-item-action {
    width: 100%;
    color: #495057;
    text-align: inherit;
}

.list-group-item-action:hover,
.list-group-item-action:focus {
    z-index: 1;
    color: #495057;
    text-decoration: none;
    background-color: #f8f9fa;
}

/* UniLink specific background colors */
.bg-unilink-primary {
    background-color: var(--color-third-darkest) !important;
    color: white !important;
}

/* Modal improvements */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(2px);
}

.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    background-color: white !important;
    opacity: 1 !important;
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
    background-color: white !important;
}

.modal-body {
    padding: 1.5rem;
    background-color: white !important;
    color: var(--color-darkest) !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    background-color: white !important;
}

.modal-title {
    color: var(--color-darkest) !important;
    font-weight: 600;
}

/* Dialog content specific styling */
[data-radix-popper-content-wrapper] {
    z-index: 1060 !important;
}

.modal-dialog {
    margin: 1.75rem auto;
    max-width: 600px;
}

.modal-lg {
    max-width: 800px;
}

/* UniLink Modal Content Styling */
.unilink-modal-content {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 800px !important;
    max-height: 90vh !important;
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    box-shadow: none !important;
    z-index: 1060 !important;
}

.unilink-modal-content .modal-dialog {
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

.unilink-modal-content .modal-content {
    background-color: white !important;
    border: none !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    opacity: 1 !important;
    width: 100% !important;
}

/* Form improvements */
.form-label {
    color: var(--color-darkest) !important;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.5rem;
    padding: 0.75rem;
    color: var(--color-darkest) !important;
    background-color: white !important;
}

.form-control:focus, .form-select:focus {
    border-color: var(--color-third-darkest);
    box-shadow: 0 0 0 0.2rem rgba(123, 199, 77, 0.25);
}

.btn-primary {
    background-color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    color: white !important;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #6ba83f !important;
    border-color: #6ba83f !important;
}

.btn-outline-primary {
    color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    color: white !important;
}

/* Bootstrap utility classes */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.me-3 {
    margin-right: 1rem !important;
}

.d-flex {
    display: flex !important;
}

.align-items-center {
    align-items: center !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.text-muted {
    color: #6c757d !important;
}

.w-75 {
    width: 75% !important;
}

.w-100 {
    width: 100% !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

/* Card styling matching prototype */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.375rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-title {
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.2;
    color: var(--color-darkest);
}

.card-text {
    color: var(--color-second-darkest);
    margin-bottom: 0;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.border-top {
    border-top: 1px solid #dee2e6 !important;
}

.pt-3 {
    padding-top: 1rem !important;
}

.small {
    font-size: 0.875em;
}

.fw-semibold {
    font-weight: 600 !important;
}

.position-relative {
    position: relative !important;
}

.position-absolute {
    position: absolute !important;
}

.top-0 {
    top: 0 !important;
}

.start-0 {
    left: 0 !important;
}

.bg-opacity-50 {
    --bs-bg-opacity: 0.5;
}

.bg-dark {
    background-color: #212529 !important;
}

.g-2 {
    --bs-gutter-x: 0.5rem;
    --bs-gutter-y: 0.5rem;
}

.col-6 {
    flex: 0 0 auto;
    width: 50%;
}

.ms-2 {
    margin-left: 0.5rem !important;
}

/* Button hover effects matching prototype */
.btn:hover {
    color: var(--color-third-darkest) !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Post card specific styling */
.post {
    background-color: white !important;
    color: var(--color-darkest) !important;
}

.post .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6;
    color: var(--color-darkest) !important;
}

.post .card-body {
    background-color: white !important;
    color: var(--color-darkest) !important;
}

.post .card-title {
    color: var(--color-darkest) !important;
    font-weight: 600;
}

.post .card-text {
    color: var(--color-darkest) !important;
    line-height: 1.6;
}

.post .text-muted {
    color: #6c757d !important;
}

.post .small {
    color: #6c757d !important;
}

/* Ensure all text elements have proper contrast */
.text-dark {
    color: var(--color-darkest) !important;
}

.text-primary {
    color: var(--color-third-darkest) !important;
}

/* List group improvements */
.list-group-item {
    background-color: white !important;
    color: var(--color-darkest) !important;
    border-color: #dee2e6;
}

.list-group-item:hover {
    background-color: #f8f9fa !important;
}

.list-group-item-action:hover {
    background-color: #f8f9fa !important;
    color: var(--color-third-darkest) !important;
}

/* Badge improvements */
.badge {
    font-weight: 500;
}

.badge.bg-primary {
    background-color: var(--color-third-darkest) !important;
}

/* Alert improvements */
.alert {
    border-radius: 0.5rem;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Dropdown improvements */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    color: var(--color-darkest) !important;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: var(--color-third-darkest) !important;
}

/* Responsive grid for media */
@media (max-width: 576px) {
    .col-6 {
        width: 100%;
    }
}

/* Bootstrap Placeholder styles */
.placeholder {
    display: inline-block;
    min-height: 1em;
    vertical-align: middle;
    cursor: wait;
    background-color: currentColor;
    opacity: 0.5;
}

.placeholder.btn::before {
    display: inline-block;
    content: "";
}

.placeholder-xs {
    min-height: 0.6em;
}

.placeholder-sm {
    min-height: 0.8em;
}

.placeholder-lg {
    min-height: 1.2em;
}

.placeholder-glow .placeholder {
    animation: placeholder-glow 2s ease-in-out infinite alternate;
}

@keyframes placeholder-glow {
    50% {
        opacity: 0.2;
    }
}

.placeholder-wave {
    -webkit-mask-image: linear-gradient(130deg, #000 40%, rgba(0, 0, 0, 0.5) 50%, #000 60%);
    mask-image: linear-gradient(130deg, #000 40%, rgba(0, 0, 0, 0.5) 50%, #000 60%);
    -webkit-mask-size: 200% 100%;
    mask-size: 200% 100%;
    animation: placeholder-wave 2s linear infinite;
}

@keyframes placeholder-wave {
    100% {
        -webkit-mask-position: -200% 0%;
        mask-position: -200% 0%;
    }
}

/* Container styling */
.container {
    width: 100%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

/* Badge styling */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.badge:empty {
    display: none;
}

.btn .badge {
    position: relative;
    top: -1px;
}

/* Button styling improvements */
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
    color: #212529;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

.btn:disabled {
    pointer-events: none;
    opacity: 0.65;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* Flex utilities */
.flex-grow-1 {
    flex-grow: 1 !important;
}

/* Dark mode styles matching prototype */
.dark body {
    background-color: var(--color-lightest);
    color: var(--color-darkest);
}

.dark .card {
    background-color: var(--color-second-darkest);
    color: var(--color-lightest);
    border-color: rgba(238, 238, 238, 0.1);
}

.dark .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: rgba(238, 238, 238, 0.1);
}

.dark .card-header.bg-primary {
    background-color: var(--color-third-darkest) !important;
    color: var(--color-darkest) !important;
}

.dark .post {
    background-color: var(--color-second-darkest);
    color: var(--color-lightest);
    border-color: rgba(238, 238, 238, 0.1);
}

.dark .btn-primary {
    background-color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    color: var(--color-darkest) !important;
}

.dark .btn-primary:hover {
    background-color: #6AB33C !important;
    border-color: #6AB33C !important;
    color: var(--color-darkest) !important;
}

.dark .btn-outline-primary {
    color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
}

.dark .btn-outline-primary:hover {
    background-color: var(--color-third-darkest) !important;
    color: var(--color-darkest) !important;
}

.dark .text-muted {
    color: rgba(238, 238, 238, 0.6) !important;
}

.dark input,
.dark textarea,
.dark select {
    background-color: #2c3037;
    color: var(--color-lightest);
    border-color: rgba(238, 238, 238, 0.2);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
    background-color: #2c3037;
    color: var(--color-lightest);
    border-color: var(--color-third-darkest);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
}

