import{r as t,j as e,K as ae,L as te}from"./app-CtrL47ne.js";import{b as ne,c as le,d as ie,B as E,X as P,e as re,U as ce,A as oe}from"./app-layout-ac8Ndnxx.js";import{T as de}from"./three-column-layout-CcmqGlmU.js";import{P as me}from"./post-card-BllH2Q8m.js";import{a as H,B as D}from"./app-logo-icon-CQl1RRw8.js";import{I as K}from"./input-Dl_K4j-H.js";import{L as _}from"./label-A-bArPtA.js";import{S as M,a as R,b as B,c as V,d as F}from"./select-DvJx-E4s.js";import{F as G}from"./filter-BgiIvQ2e.js";import{D as he,a as ue,b as xe}from"./dialog-D1kycqna.js";import{F as U}from"./file-text-B7oDA_9_.js";import{T as pe}from"./trash-2-BWQQCq0r.js";import{G as je}from"./globe-CUaCXTEi.js";import{L as be}from"./lock-qLNIosP2.js";import{P as W}from"./plus-BlpLn8Py.js";import"./index-C70TSJlT.js";import"./index-BIL34fKe.js";import"./book-open-bnIOiasB.js";import"./message-circle-BJ_C4hpy.js";import"./pin-DCoilNIW.js";import"./index-DvoShi86.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],fe=H("Image",ge);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Ne=H("RotateCcw",ve);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],_e=H("Upload",ye),X=[{value:"announcement",label:"Announcements"},{value:"discussion",label:"Discussions"},{value:"event",label:"Events"},{value:"news",label:"News"}],we=[{value:"created_at",label:"Latest"},{value:"popularity",label:"Most Popular"},{value:"trending",label:"Trending"},{value:"reactions_count",label:"Most Liked"},{value:"comments_count",label:"Most Discussed"}],Ce=["Main Campus","Isulan Campus","ACCESS Campus","Tacurong Campus","Bagumbayan Campus"];function Se({filters:u,onFiltersChange:S,onClose:i}){var C,$;const[n,f]=t.useState(u),[v,b]=t.useState([]),[L,O]=t.useState(!1);t.useEffect(()=>{(async()=>{var h;O(!0);try{const N=await fetch("/api/v1/organizations?per_page=100",{headers:{Authorization:`Bearer ${(h=document.querySelector('meta[name="api-token"]'))==null?void 0:h.getAttribute("content")}`,Accept:"application/json"}});if(N.ok){const s=await N.json();b(s.data||[])}}catch(N){console.error("Error loading organizations:",N)}finally{O(!1)}})()},[]);const g=(a,h)=>{const N=h==="all"||h==="personal"?"":h;f(s=>({...s,[a]:N}))},d=()=>{S(n),i()},w=()=>{const a={type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"};f(a),S(a)},y=()=>Object.values(n).filter(a=>a!==""&&a!=="created_at"&&a!=="desc").length,m=a=>{const h={...n};a==="sort_by"?h[a]="created_at":a==="sort_order"?h[a]="desc":h[a]="",f(h)};return e.jsxs(ne,{className:"w-full",children:[e.jsxs(le,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(G,{className:"w-5 h-5"}),e.jsx(ie,{children:"Filter Posts"}),y()>0&&e.jsxs(E,{variant:"secondary",children:[y()," active"]})]}),e.jsx(D,{variant:"ghost",size:"sm",onClick:i,children:e.jsx(P,{className:"w-4 h-4"})})]}),e.jsxs(re,{className:"space-y-6",children:[y()>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{className:"text-sm font-medium",children:"Active Filters"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[n.type&&e.jsxs(E,{variant:"outline",className:"flex items-center gap-1",children:["Type: ",(C=X.find(a=>a.value===n.type))==null?void 0:C.label,e.jsx("button",{onClick:()=>m("type"),children:e.jsx(P,{className:"w-3 h-3"})})]}),n.organization_id&&e.jsxs(E,{variant:"outline",className:"flex items-center gap-1",children:["Org: ",($=v.find(a=>a.id.toString()===n.organization_id))==null?void 0:$.name,e.jsx("button",{onClick:()=>m("organization_id"),children:e.jsx(P,{className:"w-3 h-3"})})]}),n.campus&&e.jsxs(E,{variant:"outline",className:"flex items-center gap-1",children:["Campus: ",n.campus,e.jsx("button",{onClick:()=>m("campus"),children:e.jsx(P,{className:"w-3 h-3"})})]}),n.date_from&&e.jsxs(E,{variant:"outline",className:"flex items-center gap-1",children:["From: ",n.date_from,e.jsx("button",{onClick:()=>m("date_from"),children:e.jsx(P,{className:"w-3 h-3"})})]}),n.date_to&&e.jsxs(E,{variant:"outline",className:"flex items-center gap-1",children:["To: ",n.date_to,e.jsx("button",{onClick:()=>m("date_to"),children:e.jsx(P,{className:"w-3 h-3"})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"type",children:"Post Type"}),e.jsxs(M,{value:n.type,onValueChange:a=>g("type",a),children:[e.jsx(R,{children:e.jsx(B,{placeholder:"All types"})}),e.jsxs(V,{children:[e.jsx(F,{value:"all",children:"All types"}),X.map(a=>e.jsx(F,{value:a.value,children:a.label},a.value))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"organization",children:"Organization"}),e.jsxs(M,{value:n.organization_id,onValueChange:a=>g("organization_id",a),children:[e.jsx(R,{children:e.jsx(B,{placeholder:"All organizations"})}),e.jsxs(V,{children:[e.jsx(F,{value:"all",children:"All organizations"}),v.map(a=>e.jsx(F,{value:a.id.toString(),children:a.name},a.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"campus",children:"Campus"}),e.jsxs(M,{value:n.campus,onValueChange:a=>g("campus",a),children:[e.jsx(R,{children:e.jsx(B,{placeholder:"All campuses"})}),e.jsxs(V,{children:[e.jsx(F,{value:"all",children:"All campuses"}),Ce.map(a=>e.jsx(F,{value:a,children:a},a))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"sort",children:"Sort By"}),e.jsxs(M,{value:n.sort_by,onValueChange:a=>g("sort_by",a),children:[e.jsx(R,{children:e.jsx(B,{})}),e.jsx(V,{children:we.map(a=>e.jsx(F,{value:a.value,children:a.label},a.value))})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{children:"Date Range"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx(_,{htmlFor:"date_from",className:"text-xs text-gray-500",children:"From"}),e.jsx(K,{id:"date_from",type:"date",value:n.date_from,onChange:a=>g("date_from",a.target.value)})]}),e.jsxs("div",{children:[e.jsx(_,{htmlFor:"date_to",className:"text-xs text-gray-500",children:"To"}),e.jsx(K,{id:"date_to",type:"date",value:n.date_to,onChange:a=>g("date_to",a.target.value)})]})]})]}),e.jsxs("div",{className:"flex justify-between pt-4 border-t",children:[e.jsxs(D,{variant:"outline",onClick:w,className:"flex items-center gap-2",children:[e.jsx(Ne,{className:"w-4 h-4"}),"Reset All"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(D,{variant:"outline",onClick:i,children:"Cancel"}),e.jsx(D,{onClick:d,children:"Apply Filters"})]})]})]})]})}const ke=[{value:"announcement",label:"Announcement",icon:"📢"},{value:"discussion",label:"Discussion",icon:"💬"},{value:"event",label:"Event",icon:"📅"},{value:"news",label:"News",icon:"📰"}],ze=[{value:"public",label:"Public",icon:je,description:"Anyone can see this post"},{value:"members_only",label:"Members Only",icon:ce,description:"Only organization members can see this"},{value:"private",label:"Private",icon:be,description:"Only you can see this post"}];function Fe({onClose:u,onPostCreated:S}){const[i,n]=t.useState({title:"",content:"",type:"announcement",visibility:"public",organization_id:"",is_pinned:!1,comments_enabled:!0,published_at:""}),[f,v]=t.useState([]),[b,L]=t.useState([]),[O,g]=t.useState(!1),[d,w]=t.useState({}),y=t.useRef(null);t.useState(()=>{(async()=>{var r;try{const x=await fetch("/api/v1/organizations?member=true",{headers:{Authorization:`Bearer ${(r=document.querySelector('meta[name="api-token"]'))==null?void 0:r.getAttribute("content")}`,Accept:"application/json"}});if(x.ok){const c=await x.json();L(c.data||[])}}catch(x){console.error("Error loading organizations:",x)}})()});const m=(s,r)=>{n(x=>({...x,[s]:r})),d[s]&&w(x=>({...x,[s]:""}))},C=s=>{const x=Array.from(s.target.files||[]).filter(c=>{const k=c.type.startsWith("image/")||c.type==="application/pdf"||c.type.includes("document"),o=c.size<=10*1024*1024;return k&&o});v(c=>[...c,...x])},$=s=>{v(r=>r.filter((x,c)=>c!==s))},a=()=>{const s={};return i.title.trim()||(s.title="Title is required"),i.content.trim()||(s.content="Content is required"),i.visibility==="members_only"&&!i.organization_id&&(s.organization_id="Organization is required for members-only posts"),w(s),Object.keys(s).length===0},h=async s=>{var r,x;if(s.preventDefault(),!!a()){g(!0);try{const c=new FormData;Object.entries(i).forEach(([o,z])=>{z!==""&&c.append(o,z.toString())}),f.forEach((o,z)=>{c.append(`media[${z}]`,o)});const k=await fetch("/api/v1/posts",{method:"POST",headers:{Authorization:`Bearer ${(r=document.querySelector('meta[name="api-token"]'))==null?void 0:r.getAttribute("content")}`,"X-CSRF-TOKEN":((x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content"))||""},body:c});if(k.ok){const o=await k.json();S(o.post)}else{const o=await k.json();w(o.errors||{general:"Failed to create post"})}}catch(c){console.error("Error creating post:",c),w({general:"Network error. Please try again."})}finally{g(!1)}}},N=b.find(s=>s.id.toString()===i.organization_id);return e.jsx(he,{open:!0,onOpenChange:u,children:e.jsxs(ue,{className:"unilink-modal-content",children:[e.jsx(xe,{className:"sr-only",children:"Create New Post"}),e.jsx("div",{className:"modal-dialog modal-lg",children:e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h5",{className:"modal-title d-flex align-items-center",children:[e.jsx(U,{className:"w-5 h-5 me-2"}),"Create New Post"]}),e.jsx("button",{type:"button",className:"btn-close",onClick:u})]}),e.jsx("div",{className:"modal-body",style:{maxHeight:"70vh",overflowY:"auto"},children:e.jsxs("form",{onSubmit:h,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(_,{className:"form-label",children:"Post Type"}),e.jsx("div",{className:"row g-2",children:ke.map(s=>e.jsx("div",{className:"col-6",children:e.jsx("button",{type:"button",onClick:()=>m("type",s.value),className:`btn w-100 p-3 text-start ${i.type===s.value?"btn-primary":"btn-outline-secondary"}`,children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("span",{className:"me-2",style:{fontSize:"1.2rem"},children:s.icon}),e.jsx("span",{className:"fw-medium",children:s.label})]})})},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"title",className:"form-label",children:"Title *"}),e.jsx("input",{type:"text",id:"title",className:`form-control ${d.title?"is-invalid":""}`,value:i.title,onChange:s=>m("title",s.target.value),placeholder:"Enter post title..."}),d.title&&e.jsx("div",{className:"invalid-feedback",children:d.title})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"content",className:"form-label",children:"Content *"}),e.jsx("textarea",{id:"content",className:`form-control ${d.content?"is-invalid":""}`,value:i.content,onChange:s=>m("content",s.target.value),placeholder:"What's on your mind?",rows:6}),d.content&&e.jsx("div",{className:"invalid-feedback",children:d.content})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"form-label",children:"Media"}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("button",{type:"button",className:"btn btn-outline-primary w-100",onClick:()=>{var s;return(s=y.current)==null?void 0:s.click()},children:[e.jsx(_e,{className:"w-4 h-4 me-2"}),"Add Images or Files"]}),e.jsx("input",{ref:y,type:"file",multiple:!0,accept:"image/*,.pdf,.doc,.docx",onChange:C,className:"d-none"}),f.length>0&&e.jsx("div",{className:"row g-2 mt-3",children:f.map((s,r)=>e.jsx("div",{className:"col-6",children:e.jsx("div",{className:"card p-2",children:e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center flex-grow-1 min-w-0",children:[s.type.startsWith("image/")?e.jsx(fe,{className:"w-4 h-4 text-primary"}):e.jsx(U,{className:"w-4 h-4 text-muted"}),e.jsx("span",{className:"small text-truncate",children:s.name})]}),e.jsx("button",{type:"button",className:"btn btn-sm btn-outline-danger",onClick:()=>$(r),children:e.jsx(pe,{className:"w-4 h-4"})})]})})},r))})]})]}),b.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"form-label",children:"Post as Organization (Optional)"}),e.jsxs("select",{className:`form-select ${d.organization_id?"is-invalid":""}`,value:i.organization_id,onChange:s=>m("organization_id",s.target.value),children:[e.jsx("option",{value:"",children:"Personal Post"}),b.map(s=>e.jsx("option",{value:s.id.toString(),children:s.name},s.id))]}),d.organization_id&&e.jsx("div",{className:"invalid-feedback",children:d.organization_id})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"form-label",children:"Visibility"}),e.jsx("div",{className:"d-grid gap-2",children:ze.map(s=>{const r=s.icon;return e.jsx("button",{type:"button",onClick:()=>m("visibility",s.value),disabled:s.value==="members_only"&&!i.organization_id,className:`btn p-3 text-start ${i.visibility===s.value?"btn-primary":"btn-outline-secondary"} ${s.value==="members_only"&&!i.organization_id?"disabled":""}`,children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(r,{className:"w-5 h-5 me-3"}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-medium",children:s.label}),e.jsx("div",{className:"small text-muted",children:s.description})]})]})},s.value)})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label mb-0",children:"Enable Comments"}),e.jsx("p",{className:"small text-muted mb-0",children:"Allow users to comment on this post"})]}),e.jsx("div",{className:"form-check form-switch",children:e.jsx("input",{className:"form-check-input",type:"checkbox",id:"comments_enabled",checked:i.comments_enabled,onChange:s=>m("comments_enabled",s.target.checked)})})]}),N&&e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label mb-0",children:"Pin Post"}),e.jsx("p",{className:"small text-muted mb-0",children:"Pin this post to the top of the feed"})]}),e.jsx("div",{className:"form-check form-switch",children:e.jsx("input",{className:"form-check-input",type:"checkbox",id:"is_pinned",checked:i.is_pinned,onChange:s=>m("is_pinned",s.target.checked)})})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"published_at",className:"form-label",children:"Schedule Post (Optional)"}),e.jsx("input",{type:"datetime-local",id:"published_at",className:"form-control",value:i.published_at,onChange:s=>m("published_at",s.target.value),min:new Date().toISOString().slice(0,16)}),e.jsx("div",{className:"form-text",children:"Leave empty to publish immediately"})]}),d.general&&e.jsx("div",{className:"alert alert-danger",children:d.general})]})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",className:"btn btn-secondary",onClick:u,children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:O,onClick:h,children:O?"Creating...":"Create Post"})]})]})})]})})}const Oe=[{title:"Feed",href:"/feed"}];function Je({initialPosts:u,feedType:S="personalized"}){var Y;const{props:i}=ae(),n=(Y=i.auth)==null?void 0:Y.api_token,[f,v]=t.useState((u==null?void 0:u.data)||[]),[b,L]=t.useState(!1),[O,g]=t.useState(!1),[d,w]=t.useState(u?u.current_page<u.last_page:!0),[y,m]=t.useState((u==null?void 0:u.current_page)||1),[C,$]=t.useState(""),[a,h]=t.useState(!1),[N,s]=t.useState(!1),[r,x]=t.useState(S);console.log("Feed component props:",{initialPosts:u,feedType:S,posts:f});const[c,k]=t.useState({type:"",organization_id:"",campus:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"}),o=t.useCallback(async(l=1,j=!1)=>{L(!0);try{const A=new URLSearchParams({page:l.toString(),per_page:"15",...C&&{search:C},...Object.fromEntries(Object.entries(c).filter(([p,q])=>q!==""))});let T="/api/v1/posts";r==="personalized"?T="/api/v1/feed":r==="trending"&&(T="/api/v1/feed/trending");const I=await fetch(`${T}?${A}`,{headers:{Authorization:`Bearer ${n}`,Accept:"application/json"}});if(I.ok){const p=await I.json();v(j?q=>[...q,...p.data]:p.data),m(p.current_page),w(p.current_page<p.last_page)}}catch(A){console.error("Error loading posts:",A)}finally{L(!1)}},[r,C,c]),z=t.useCallback(()=>{!b&&d&&o(y+1,!0)},[b,d,y,o]);t.useCallback(async()=>{g(!0),await o(1,!1),g(!1)},[o]),t.useCallback(l=>{l.preventDefault(),o(1,!1)},[o]);const Q=t.useCallback(l=>{k(l),o(1,!1)},[o]),J=t.useCallback(l=>{x(l),m(1),v([])},[]);t.useEffect(()=>{o(1,!1)},[r]),t.useEffect(()=>{const l=()=>{window.innerHeight+document.documentElement.scrollTop!==document.documentElement.offsetHeight||b||z()};return window.addEventListener("scroll",l),()=>window.removeEventListener("scroll",l)},[z,b]);const Z=async(l,j)=>{var A;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`,"X-CSRF-TOKEN":((A=document.querySelector('meta[name="csrf-token"]'))==null?void 0:A.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:l,type:j})})).ok&&v(I=>I.map(p=>p.id===l?{...p,reactions_count:p.user_reaction===j?p.reactions_count-1:p.reactions_count+(p.user_reaction?0:1),user_reaction:p.user_reaction===j?void 0:j}:p))}catch(T){console.error("Error toggling reaction:",T)}},ee=l=>{window.location.href=`/posts/${l}#comments`},se=l=>{var j;(j=navigator.share)==null||j.call(navigator,{title:"UniLink Post",url:`${window.location.origin}/posts/${l}`})};return e.jsxs(oe,{breadcrumbs:Oe,children:[e.jsx(te,{title:"Feed"}),e.jsxs(de,{children:[e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("button",{onClick:()=>J("personalized"),className:`btn btn-sm me-2 ${r==="personalized"?"btn-primary":"btn-outline-primary"}`,children:"All"}),e.jsxs("button",{onClick:()=>h(!a),className:"btn btn-outline-primary btn-sm",children:[e.jsx(G,{className:"w-4 h-4 me-1"}),"Filter"]})]}),e.jsxs("button",{onClick:()=>s(!0),className:"btn btn-primary btn-sm",children:[e.jsx(W,{className:"w-4 h-4 me-1"}),"Create Post"]})]})})}),a&&e.jsx(Se,{filters:c,onFiltersChange:Q,onClose:()=>h(!1)}),e.jsxs("div",{className:"feed-content",children:[f.length>0?f.map(l=>e.jsx(me,{post:l,onReact:Z,onComment:ee,onShare:se},l.id)):b?null:e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx(U,{className:"w-12 h-12 text-muted mx-auto mb-4"}),e.jsx("h3",{className:"h5 mb-2",children:"No posts found"}),e.jsx("p",{className:"text-muted mb-4",children:"Be the first to add a post!"}),e.jsxs("button",{onClick:()=>s(!0),className:"btn btn-primary",children:[e.jsx(W,{className:"w-4 h-4 me-2"}),"Create Post"]})]})}),b&&e.jsx("div",{children:[...Array(3)].map((l,j)=>e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx("div",{className:"placeholder-glow",children:e.jsx("div",{className:"placeholder rounded-circle me-3",style:{width:"40px",height:"40px"}})}),e.jsxs("div",{className:"placeholder-glow flex-grow-1",children:[e.jsx("div",{className:"placeholder col-4 mb-2"}),e.jsx("div",{className:"placeholder col-3"})]})]}),e.jsxs("div",{className:"placeholder-glow",children:[e.jsx("div",{className:"placeholder col-8 mb-3"}),e.jsx("div",{className:"placeholder col-12 mb-3",style:{height:"80px"}}),e.jsxs("div",{className:"d-flex",children:[e.jsx("div",{className:"placeholder col-2 me-2"}),e.jsx("div",{className:"placeholder col-2 me-2"}),e.jsx("div",{className:"placeholder col-2"})]})]})]})},j))})]})]}),N&&e.jsx(Fe,{onClose:()=>s(!1),onPostCreated:l=>{v(j=>[l,...j]),s(!1)}})]})}export{Je as default};
