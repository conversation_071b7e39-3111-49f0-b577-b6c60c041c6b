import{r as m,j as e,L as T,$ as B}from"./app-BYdmc_gi.js";import{b as re,c as ne,d as ie,e as oe,B as ce,A as P}from"./app-layout-B0AAWw2-.js";import{S as le,P as de}from"./post-card-Bq48uD4L.js";import{a as $,c as O,B as w}from"./app-logo-icon-D4PatI0y.js";import{T as q}from"./textarea-DC9s2JiL.js";import{c as ue,e as me,d as I}from"./index-CutG6Jxx.js";import{P as M}from"./index-CGyr8X69.js";import{M as H}from"./message-circle-C-hErto7.js";import{H as he}from"./pin-B6M04fXp.js";import{S as V}from"./square-pen-DdNIufgZ.js";import{T as W,a as xe,C as pe}from"./trash-2-CnGShcPQ.js";import{A as D}from"./arrow-left-B5o86YPh.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],je=$("Ellipsis",fe);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]],ve=$("Flag",ge);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["polyline",{points:"9 17 4 12 9 7",key:"hvgpf2"}],["path",{d:"M20 18v-2a4 4 0 0 0-4-4H4",key:"5vmcpk"}]],ye=$("Reply",Ne);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],J=$("Send",we);var K="Avatar",[Se,Je]=ue(K),[be,G]=Se(K),Q=m.forwardRef((i,o)=>{const{__scopeAvatar:s,...l}=i,[x,h]=m.useState("idle");return e.jsx(be,{scope:s,imageLoadingStatus:x,onImageLoadingStatusChange:h,children:e.jsx(M.span,{...l,ref:o})})});Q.displayName=K;var U="AvatarImage",Y=m.forwardRef((i,o)=>{const{__scopeAvatar:s,src:l,onLoadingStatusChange:x=()=>{},...h}=i,g=G(U,s),u=ke(l,h.referrerPolicy),p=me(S=>{x(S),g.onImageLoadingStatusChange(S)});return I(()=>{u!=="idle"&&p(u)},[u,p]),u==="loaded"?e.jsx(M.img,{...h,ref:o,src:l}):null});Y.displayName=U;var Z="AvatarFallback",ee=m.forwardRef((i,o)=>{const{__scopeAvatar:s,delayMs:l,...x}=i,h=G(Z,s),[g,u]=m.useState(l===void 0);return m.useEffect(()=>{if(l!==void 0){const p=window.setTimeout(()=>u(!0),l);return()=>window.clearTimeout(p)}},[l]),g&&h.imageLoadingStatus!=="loaded"?e.jsx(M.span,{...x,ref:o}):null});ee.displayName=Z;function ke(i,o){const[s,l]=m.useState("idle");return I(()=>{if(!i){l("error");return}let x=!0;const h=new window.Image,g=u=>()=>{x&&l(u)};return l("loading"),h.onload=g("loaded"),h.onerror=g("error"),h.src=i,o&&(h.referrerPolicy=o),()=>{x=!1}},[i,o]),s}var _e=Q,Ce=Y,Ae=ee;function Ee({className:i,...o}){return e.jsx(_e,{"data-slot":"avatar",className:O("relative flex size-8 shrink-0 overflow-hidden rounded-full",i),...o})}function Re({className:i,...o}){return e.jsx(Ce,{"data-slot":"avatar-image",className:O("aspect-square size-full",i),...o})}function Le({className:i,...o}){return e.jsx(Ae,{"data-slot":"avatar-fallback",className:O("bg-muted flex size-full items-center justify-center rounded-full",i),...o})}function $e({postId:i,comments:o,currentUser:s,onCommentAdded:l,onCommentUpdated:x,onCommentDeleted:h}){const[g,u]=m.useState(o),[p,S]=m.useState(""),[k,C]=m.useState(null),[z,A]=m.useState(null),[E,_]=m.useState(""),[v,n]=m.useState(new Set),[N,R]=m.useState(!1),L=async(t,r)=>{var a,y;if(t.trim()){R(!0);try{const f=await fetch("/api/v1/comments",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,"X-CSRF-TOKEN":((y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"))||""},body:JSON.stringify({content:t,post_id:i,parent_id:r})});if(f.ok){const j=await f.json();r?(u(c=>c.map(d=>d.id===r?{...d,replies:[...d.replies||[],j.comment]}:d)),n(c=>new Set([...c,r]))):u(c=>[j.comment,...c]),S(""),C(null),l==null||l(j.comment)}}catch(f){console.error("Error adding comment:",f)}finally{R(!1)}}},b=async(t,r)=>{var a,y;if(r.trim())try{const f=await fetch(`/api/v1/comments/${t}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,"X-CSRF-TOKEN":((y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"))||""},body:JSON.stringify({content:r})});if(f.ok){const j=await f.json();u(c=>c.map(d=>d.id===t?{...d,...j.comment}:d.replies?{...d,replies:d.replies.map(F=>F.id===t?{...F,...j.comment}:F)}:d)),A(null),_(""),x==null||x(j.comment)}}catch(f){console.error("Error editing comment:",f)}},te=async t=>{var r,a;if(confirm("Are you sure you want to delete this comment?"))try{(await fetch(`/api/v1/comments/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${(r=document.querySelector('meta[name="api-token"]'))==null?void 0:r.getAttribute("content")}`,"X-CSRF-TOKEN":((a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content"))||""}})).ok&&(u(f=>f.filter(j=>j.id===t?!1:(j.replies&&(j.replies=j.replies.filter(c=>c.id!==t)),!0))),h==null||h(t))}catch(y){console.error("Error deleting comment:",y)}},se=async(t,r)=>{var a,y;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,"X-CSRF-TOKEN":((y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"comment",reactable_id:t,type:r})})).ok&&u(j=>j.map(c=>c.id===t?{...c,reactions_count:c.user_reaction===r?c.reactions_count-1:c.reactions_count+(c.user_reaction?0:1),user_reaction:c.user_reaction===r?void 0:r}:c.replies?{...c,replies:c.replies.map(d=>d.id===t?{...d,reactions_count:d.user_reaction===r?d.reactions_count-1:d.reactions_count+(d.user_reaction?0:1),user_reaction:d.user_reaction===r?void 0:r}:d)}:c))}catch(f){console.error("Error toggling reaction:",f)}},ae=t=>{n(r=>{const a=new Set(r);return a.has(t)?a.delete(t):a.add(t),a})},X=({comment:t,isReply:r=!1})=>e.jsx("div",{className:`${r?"ml-8 border-l-2 border-gray-100 pl-4":""}`,children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsxs(Ee,{className:"w-8 h-8",children:[e.jsx(Re,{src:t.user.avatar?`/storage/${t.user.avatar}`:void 0}),e.jsx(Le,{children:t.user.name.split(" ").map(a=>a[0]).join("").toUpperCase()})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"font-medium text-sm",children:t.user.name}),e.jsxs("div",{className:"flex items-center gap-2",children:[t.is_edited&&e.jsx(ce,{variant:"outline",className:"text-xs",children:"Edited"}),e.jsx("span",{className:"text-xs text-gray-500",children:new Date(t.created_at).toLocaleString()})]})]}),z===t.id?e.jsxs("div",{className:"space-y-2",children:[e.jsx(q,{value:E,onChange:a=>_(a.target.value),rows:3}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(w,{size:"sm",onClick:()=>b(t.id,E),children:"Save"}),e.jsx(w,{size:"sm",variant:"outline",onClick:()=>{A(null),_("")},children:"Cancel"})]})]}):e.jsx("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:t.content})]}),e.jsxs("div",{className:"flex items-center gap-4 mt-2 text-sm",children:[e.jsxs(w,{variant:"ghost",size:"sm",onClick:()=>se(t.id,"like"),className:`h-auto p-1 ${t.user_reaction==="like"?"text-red-600":"text-gray-500"}`,children:[e.jsx(he,{className:`w-4 h-4 mr-1 ${t.user_reaction==="like"?"fill-current":""}`}),t.reactions_count]}),!r&&e.jsxs(w,{variant:"ghost",size:"sm",onClick:()=>C(k===t.id?null:t.id),className:"h-auto p-1 text-gray-500",children:[e.jsx(ye,{className:"w-4 h-4 mr-1"}),"Reply"]}),t.can_edit&&e.jsx(w,{variant:"ghost",size:"sm",onClick:()=>{A(t.id),_(t.content)},className:"h-auto p-1 text-gray-500",children:e.jsx(V,{className:"w-4 h-4"})}),t.can_delete&&e.jsx(w,{variant:"ghost",size:"sm",onClick:()=>te(t.id),className:"h-auto p-1 text-red-500",children:e.jsx(W,{className:"w-4 h-4"})})]}),k===t.id&&e.jsxs("div",{className:"mt-3 space-y-2",children:[e.jsx(q,{placeholder:"Write a reply...",value:p,onChange:a=>S(a.target.value),rows:3}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(w,{size:"sm",onClick:()=>L(p,t.id),disabled:!p.trim()||N,children:[e.jsx(J,{className:"w-4 h-4 mr-1"}),"Reply"]}),e.jsx(w,{size:"sm",variant:"outline",onClick:()=>{C(null),S("")},children:"Cancel"})]})]}),t.replies&&t.replies.length>0&&e.jsxs("div",{className:"mt-3",children:[e.jsx(w,{variant:"ghost",size:"sm",onClick:()=>ae(t.id),className:"h-auto p-1 text-blue-600",children:v.has(t.id)?e.jsxs(e.Fragment,{children:[e.jsx(xe,{className:"w-4 h-4 mr-1"}),"Hide ",t.replies.length," replies"]}):e.jsxs(e.Fragment,{children:[e.jsx(pe,{className:"w-4 h-4 mr-1"}),"Show ",t.replies.length," replies"]})}),v.has(t.id)&&e.jsx("div",{className:"mt-3 space-y-3",children:t.replies.map(a=>e.jsx(X,{comment:a,isReply:!0},a.id))})]})]})]})});return e.jsxs(re,{children:[e.jsx(ne,{children:e.jsxs(ie,{className:"flex items-center gap-2",children:[e.jsx(H,{className:"w-5 h-5"}),"Comments (",g.length,")"]})}),e.jsxs(oe,{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(q,{placeholder:"Write a comment...",value:p,onChange:t=>S(t.target.value),rows:3}),e.jsxs(w,{onClick:()=>L(p),disabled:!p.trim()||N,children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Post Comment"]})]}),e.jsx("div",{className:"space-y-6",children:g.length>0?g.map(t=>e.jsx(X,{comment:t},t.id)):e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(H,{className:"w-12 h-12 mx-auto mb-2 text-gray-300"}),e.jsx("p",{children:"No comments yet. Be the first to comment!"})]})})]})]})}function Ie({postId:i,post:o}){const[s,l]=m.useState(o||null),[x,h]=m.useState(!o),[g,u]=m.useState(null),[p,S]=m.useState(!1),k=[{title:"Posts",href:"/posts"},{title:(s==null?void 0:s.title)||"Post",href:`/posts/${i}`}];m.useEffect(()=>{o||C()},[i]);const C=async()=>{var v;h(!0),u(null);try{const n=await fetch(`/api/v1/posts/${i}`,{headers:{Authorization:`Bearer ${(v=document.querySelector('meta[name="api-token"]'))==null?void 0:v.getAttribute("content")}`,Accept:"application/json"}});if(n.ok){const N=await n.json();l(N)}else n.status===404?u("Post not found"):u("Failed to load post")}catch(n){console.error("Error loading post:",n),u("An unexpected error occurred")}finally{h(!1)}},z=async(v,n)=>{var N,R;if(s)try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(N=document.querySelector('meta[name="api-token"]'))==null?void 0:N.getAttribute("content")}`,"X-CSRF-TOKEN":((R=document.querySelector('meta[name="csrf-token"]'))==null?void 0:R.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:v,type:n})})).ok&&l(b=>b?{...b,reactions_count:b.user_reaction===n?b.reactions_count-1:b.reactions_count+(b.user_reaction?0:1),user_reaction:b.user_reaction===n?void 0:n}:null)}catch(L){console.error("Error toggling reaction:",L)}},A=v=>{const n=document.getElementById("comments");n&&n.scrollIntoView({behavior:"smooth"})},E=async v=>{const n=`${window.location.origin}/posts/${v}`;if(navigator.share)try{await navigator.share({title:s==null?void 0:s.title,text:(s==null?void 0:s.content.substring(0,100))+"...",url:n})}catch{}else try{await navigator.clipboard.writeText(n),alert("Link copied to clipboard!")}catch(N){console.error("Failed to copy link:",N)}},_=async()=>{var v,n;if(!(!s||!confirm("Are you sure you want to delete this post?")))try{(await fetch(`/api/v1/posts/${s.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${(v=document.querySelector('meta[name="api-token"]'))==null?void 0:v.getAttribute("content")}`,"X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""}})).ok?window.location.href="/posts":alert("Failed to delete post")}catch(N){console.error("Error deleting post:",N),alert("An error occurred while deleting the post")}};return x?e.jsxs(P,{breadcrumbs:k,children:[e.jsx(T,{title:"Loading..."}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-md-8 mx-auto",children:e.jsxs("div",{className:"text-center py-5",children:[e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"mt-3 text-muted",children:"Loading post..."})]})})})})})})]}):g||!s?e.jsxs(P,{breadcrumbs:k,children:[e.jsx(T,{title:"Error"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-md-8 mx-auto",children:e.jsx("div",{className:"card shadow-sm",children:e.jsxs("div",{className:"card-body text-center py-5",children:[e.jsx("h3",{className:"h5 mb-3",children:"Post Not Found"}),e.jsx("p",{className:"text-muted mb-4",children:g||"The post you are looking for does not exist or has been removed."}),e.jsxs(B,{href:"/posts",className:"btn btn-primary",children:[e.jsx(D,{className:"w-4 h-4 me-2"}),"Back to Posts"]})]})})})})})})})]}):e.jsxs(P,{breadcrumbs:k,children:[e.jsx(T,{title:s.title}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-md-8 mx-auto",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsxs(B,{href:"/posts",className:"btn btn-outline-secondary",children:[e.jsx(D,{className:"w-4 h-4 me-2"}),"Back to Posts"]}),e.jsxs("div",{className:"dropdown",children:[e.jsx("button",{className:"btn btn-outline-secondary",type:"button",onClick:()=>S(!p),children:e.jsx(je,{className:"w-4 h-4"})}),p&&e.jsxs("div",{className:"dropdown-menu dropdown-menu-end show",children:[e.jsxs("button",{className:"dropdown-item",onClick:()=>E(s.id),children:[e.jsx(le,{className:"w-4 h-4 me-2"}),"Share"]}),s.can_edit&&e.jsxs(B,{href:`/posts/${s.id}/edit`,className:"dropdown-item",children:[e.jsx(V,{className:"w-4 h-4 me-2"}),"Edit"]}),s.can_delete&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"dropdown-divider"}),e.jsxs("button",{className:"dropdown-item text-danger",onClick:_,children:[e.jsx(W,{className:"w-4 h-4 me-2"}),"Delete"]})]}),!s.can_edit&&!s.can_delete&&e.jsxs("button",{className:"dropdown-item",children:[e.jsx(ve,{className:"w-4 h-4 me-2"}),"Report"]})]})]})]}),e.jsx(de,{post:s,onReact:z,onComment:A,onShare:E}),s.comments_enabled&&e.jsx("div",{id:"comments",className:"mt-4",children:e.jsx($e,{postId:s.id})}),e.jsxs("div",{className:"mt-5",children:[e.jsx("h4",{className:"h5 mb-3",children:"Related Posts"}),e.jsx("div",{className:"card shadow-sm",children:e.jsx("div",{className:"card-body text-center py-4",children:e.jsx("p",{className:"text-muted mb-0",children:"Related posts will be shown here"})})})]})]})})})})})]})}export{Ie as default};
