import{r as s,j as t}from"./app-BYRFhzWn.js";import{c as a}from"./app-logo-icon-BTP1W0LK.js";const i=s.forwardRef(({className:e,...r},o)=>t.jsx("textarea",{className:a("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...r}));i.displayName="Textarea";export{i as T};
