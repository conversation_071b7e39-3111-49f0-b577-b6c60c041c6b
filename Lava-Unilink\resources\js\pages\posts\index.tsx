import { useState, useEffect } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    Plus,
    Search,
    Filter,
    FileText,
    Users,
    Calendar
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Posts',
        href: '/posts',
    },
];

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
}

interface PostsProps {
    initialPosts?: {
        data: Post[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function PostsIndex({ initialPosts }: PostsProps) {
    const [posts, setPosts] = useState<Post[]>(initialPosts?.data || []);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(initialPosts ? initialPosts.current_page < initialPosts.last_page : true);
    const [currentPage, setCurrentPage] = useState(initialPosts?.current_page || 1);
    const [searchQuery, setSearchQuery] = useState('');
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState({
        type: '',
        organization_id: '',
        campus: '',
        date_from: '',
        date_to: '',
        sort_by: 'created_at',
        sort_order: 'desc'
    });

    // Load posts
    const loadPosts = async (page = 1, append = false) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '15',
                ...(searchQuery && { search: searchQuery }),
                ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== ''))
            });

            const response = await fetch(`/api/v1/posts?${params}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (append) {
                    setPosts(prev => [...prev, ...data.data]);
                } else {
                    setPosts(data.data);
                }
                setCurrentPage(data.current_page);
                setHasMore(data.current_page < data.last_page);
            }
        } catch (error) {
            console.error('Error loading posts:', error);
        } finally {
            setLoading(false);
        }
    };

    // Load more posts
    const loadMore = () => {
        if (!loading && hasMore) {
            loadPosts(currentPage + 1, true);
        }
    };

    // Handle search
    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        loadPosts(1, false);
    };

    // Handle filter changes
    const handleFilterChange = (newFilters: typeof filters) => {
        setFilters(newFilters);
        loadPosts(1, false);
    };

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { 
                                ...post, 
                                reactions_count: post.user_reaction === reactionType 
                                    ? post.reactions_count - 1 
                                    : post.reactions_count + (post.user_reaction ? 0 : 1),
                                user_reaction: post.user_reaction === reactionType ? undefined : reactionType
                            }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        navigator.share?.({
            title: 'UniLink Post',
            url: `${window.location.origin}/posts/${postId}`
        });
    };

    // Load initial data if not provided
    useEffect(() => {
        if (!initialPosts) {
            loadPosts();
        }
    }, []);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Posts" />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        <div className="row">
                            {/* Main Content */}
                            <div className="col-md-8 mx-auto">
                                {/* Header */}
                                <div className="d-flex justify-content-between align-items-center mb-4">
                                    <h1 className="h3 mb-0">All Posts</h1>
                                    <Link href="/posts/create" className="btn btn-primary">
                                        <Plus className="w-4 h-4 me-2" />
                                        Create Post
                                    </Link>
                                </div>

                                {/* Search and Filters */}
                                <div className="card shadow-sm mb-4">
                                    <div className="card-body">
                                        <form onSubmit={handleSearch} className="d-flex gap-2 mb-3">
                                            <div className="flex-grow-1">
                                                <input
                                                    type="text"
                                                    className="form-control"
                                                    placeholder="Search posts..."
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                />
                                            </div>
                                            <button type="submit" className="btn btn-outline-primary">
                                                <Search className="w-4 h-4" />
                                            </button>
                                            <button
                                                type="button"
                                                onClick={() => setShowFilters(!showFilters)}
                                                className="btn btn-outline-secondary"
                                            >
                                                <Filter className="w-4 h-4" />
                                            </button>
                                        </form>

                                        {showFilters && (
                                            <div className="border-top pt-3">
                                                <div className="row g-3">
                                                    <div className="col-md-4">
                                                        <select
                                                            className="form-select"
                                                            value={filters.type}
                                                            onChange={(e) => setFilters({...filters, type: e.target.value})}
                                                        >
                                                            <option value="">All Types</option>
                                                            <option value="announcement">Announcement</option>
                                                            <option value="discussion">Discussion</option>
                                                            <option value="event">Event</option>
                                                            <option value="news">News</option>
                                                        </select>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <select
                                                            className="form-select"
                                                            value={filters.sort_by}
                                                            onChange={(e) => setFilters({...filters, sort_by: e.target.value})}
                                                        >
                                                            <option value="created_at">Latest</option>
                                                            <option value="reactions_count">Most Liked</option>
                                                            <option value="comments_count">Most Commented</option>
                                                        </select>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <button
                                                            type="button"
                                                            onClick={() => {
                                                                setFilters({
                                                                    type: '',
                                                                    organization_id: '',
                                                                    campus: '',
                                                                    date_from: '',
                                                                    date_to: '',
                                                                    sort_by: 'created_at',
                                                                    sort_order: 'desc'
                                                                });
                                                                setSearchQuery('');
                                                                loadPosts(1, false);
                                                            }}
                                                            className="btn btn-outline-secondary w-100"
                                                        >
                                                            Clear Filters
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Posts List */}
                                <div className="posts-list">
                                    {posts.length > 0 ? (
                                        posts.map((post) => (
                                            <PostCard
                                                key={post.id}
                                                post={post}
                                                onReact={handleReaction}
                                                onComment={handleComment}
                                                onShare={handleShare}
                                            />
                                        ))
                                    ) : !loading ? (
                                        <div className="card shadow-sm">
                                            <div className="card-body text-center py-5">
                                                <FileText className="w-12 h-12 text-muted mx-auto mb-3" />
                                                <h3 className="h5 mb-2">No posts found</h3>
                                                <p className="text-muted mb-4">
                                                    {searchQuery || Object.values(filters).some(v => v) 
                                                        ? 'Try adjusting your search or filters.' 
                                                        : 'Be the first to create a post!'}
                                                </p>
                                                <Link href="/posts/create" className="btn btn-primary">
                                                    <Plus className="w-4 h-4 me-2" />
                                                    Create Post
                                                </Link>
                                            </div>
                                        </div>
                                    ) : null}

                                    {/* Loading */}
                                    {loading && (
                                        <div className="text-center py-4">
                                            <div className="spinner-border text-primary" role="status">
                                                <span className="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    )}

                                    {/* Load More */}
                                    {hasMore && !loading && posts.length > 0 && (
                                        <div className="text-center mt-4">
                                            <button onClick={loadMore} className="btn btn-outline-primary">
                                                Load More Posts
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
