import{j as e,L as a}from"./app-CSPm8fiq.js";import{A as s}from"./app-layout-CtjvFf2e.js";import{T as r}from"./three-column-layout-DMiRj9zS.js";import"./app-logo-icon-RBJwxtah.js";import"./index-Jg6R2Vip.js";import"./index-B4tLxb2k.js";import"./book-open-qP3kpa8t.js";import"./message-circle-Dp-T4UGq.js";const t=[{title:"Example Page",href:"/example"}];function h(){return e.jsxs(s,{breadcrumbs:t,children:[e.jsx(a,{title:"Example Page"}),e.jsx(r,{children:e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsx("h4",{className:"mb-0",children:"Example with Default Sidebars"})}),e.jsxs("div",{className:"card-body",children:[e.jsx("p",{children:"This page uses the default left and right sidebars from the ThreeColumnLayout component."}),e.jsx("p",{children:"The layout automatically provides consistent navigation and quick links."})]})]})})]})}export{h as default};
