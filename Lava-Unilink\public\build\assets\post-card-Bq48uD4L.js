import{r as d,j as e}from"./app-BYdmc_gi.js";import{P as N,H as w}from"./pin-B6M04fXp.js";import{M as v}from"./message-circle-C-hErto7.js";import{a as k}from"./app-logo-icon-D4PatI0y.js";import{C as p}from"./app-layout-B0AAWw2-.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],S=k("Share2",C);function E({post:s,onReact:m,onComment:i,onShare:l}){const[M,_]=d.useState(!1),[r,x]=d.useState(!1),[c,h]=d.useState(null),g=t=>{switch(t.toLowerCase()){case"announcement":return"bg-unilink-primary text-white";case"event":return"bg-blue-500 text-white";case"discussion":return"bg-green-500 text-white";case"news":return"bg-orange-500 text-white";default:return"bg-unilink-secondary text-white"}},u=s.content.length>300,b=t=>t.length>300?t.substring(0,300)+"...":t,j=t=>{const n=new Date(t),o=new Date,a=Math.floor((o.getTime()-n.getTime())/(1e3*60*60));return a<1?`${Math.floor((o.getTime()-n.getTime())/6e4)}m ago`:a<24?`${a}h ago`:a<168?`${Math.floor(a/24)}d ago`:n.toLocaleDateString()},f=t=>{m&&(m(s.id,t),h(c===t?null:t))};return e.jsxs("div",{className:"card shadow-sm mb-4 post",children:[e.jsx("div",{className:"card-header bg-light",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:s.user.avatar?`/storage/${s.user.avatar}`:"/img/profilepic.jpg",className:"rounded-circle me-2",width:"40",height:"40",style:{objectFit:"cover"},alt:"Profile"}),e.jsxs("div",{children:[e.jsx("h5",{className:"card-title mb-0",children:s.title}),e.jsxs("small",{className:"text-muted",children:["Posted by ",s.user.name," on ",j(s.published_at||s.created_at),s.visibility!=="public"&&e.jsxs("span",{className:"ms-2",children:[e.jsx("i",{className:"fas fa-lock text-muted"}),s.visibility==="organization"?" Organization Only":" Campus Only"]})]})]})]}),s.organization&&e.jsx("span",{className:"badge bg-unilink-primary text-white",children:s.organization.name})]})}),e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"mb-3",children:[e.jsx("p",{className:"card-text",style:{whiteSpace:"pre-wrap"},children:r?s.content:b(s.content)}),u&&e.jsx("button",{onClick:()=>x(!r),className:"btn btn-link p-0 text-decoration-none",style:{color:"var(--color-third-darkest)",fontSize:"0.875rem"},children:r?"Show Less":"Read More"})]}),e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx("span",{className:`badge ${g(s.type)}`,children:s.type}),s.is_pinned&&e.jsxs("span",{className:"badge bg-warning text-dark ms-2",children:[e.jsx(N,{className:"w-3 h-3 me-1"}),"Pinned"]})]}),s.media&&s.media.length>0&&e.jsx("div",{className:"mb-3",children:e.jsx("div",{className:"row g-2",children:s.media.slice(0,4).map((t,n)=>e.jsxs("div",{className:"col-6 position-relative",children:[t.type.startsWith("image/")?e.jsx("img",{src:`/storage/${t.path}`,alt:t.name,className:"w-100 rounded",style:{height:"128px",objectFit:"cover"}}):e.jsx("div",{className:"w-100 bg-light rounded d-flex align-items-center justify-content-center",style:{height:"128px"},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{style:{fontSize:"2rem"},className:"mb-1",children:"📄"}),e.jsx("p",{className:"small text-muted text-truncate px-2",children:t.name})]})}),n===3&&s.media.length>4&&e.jsx("div",{className:"position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 rounded d-flex align-items-center justify-content-center",children:e.jsxs("span",{className:"text-white fw-semibold",children:["+",s.media.length-4," more"]})})]},n))})}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center pt-3 border-top",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsxs("button",{onClick:()=>f("like"),className:`btn btn-sm me-3 d-flex align-items-center ${c==="like"?"text-danger":"text-muted"}`,style:{border:"none",background:"none"},children:[e.jsx(w,{className:`w-4 h-4 me-1 ${c==="like"?"fill-current":""}`}),e.jsx("span",{children:s.reactions_count})]}),e.jsxs("button",{onClick:()=>i==null?void 0:i(s.id),className:"btn btn-sm me-3 d-flex align-items-center text-muted",disabled:!s.comments_enabled,style:{border:"none",background:"none"},children:[e.jsx(v,{className:"w-4 h-4 me-1"}),e.jsx("span",{children:s.comments_count})]}),e.jsxs("button",{onClick:()=>l==null?void 0:l(s.id),className:"btn btn-sm d-flex align-items-center text-muted",style:{border:"none",background:"none"},children:[e.jsx(S,{className:"w-4 h-4 me-1"}),e.jsx("span",{children:"Share"})]})]}),s.type==="event"&&e.jsxs("div",{className:"d-flex align-items-center small text-muted",children:[e.jsx(p,{className:"w-3 h-3 me-1"}),"Event"]})]})]})]})}export{E as P,S};
