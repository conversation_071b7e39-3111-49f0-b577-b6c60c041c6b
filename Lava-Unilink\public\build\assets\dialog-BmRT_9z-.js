import{j as t}from"./app-2kjRWvs5.js";import{p as i,T as l,q as r,r as s,X as d,s as c,t as g,v as u,O as f}from"./app-layout-Susg8ZLQ.js";import{c as o}from"./app-logo-icon-ylztpkIq.js";function D({...a}){return t.jsx(i,{"data-slot":"dialog",...a})}function b({...a}){return t.jsx(l,{"data-slot":"dialog-trigger",...a})}function x({...a}){return t.jsx(u,{"data-slot":"dialog-portal",...a})}function h({...a}){return t.jsx(s,{"data-slot":"dialog-close",...a})}function m({className:a,...e}){return t.jsx(f,{"data-slot":"dialog-overlay",className:o("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",a),...e})}function y({className:a,children:e,...n}){return t.jsxs(x,{"data-slot":"dialog-portal",children:[t.jsx(m,{}),t.jsxs(r,{"data-slot":"dialog-content",className:o("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...n,children:[e,t.jsxs(s,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[t.jsx(d,{}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function N({className:a,...e}){return t.jsx("div",{"data-slot":"dialog-header",className:o("flex flex-col gap-2 text-center sm:text-left",a),...e})}function z({className:a,...e}){return t.jsx("div",{"data-slot":"dialog-footer",className:o("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...e})}function w({className:a,...e}){return t.jsx(c,{"data-slot":"dialog-title",className:o("text-lg leading-none font-semibold",a),...e})}function C({className:a,...e}){return t.jsx(g,{"data-slot":"dialog-description",className:o("text-muted-foreground text-sm",a),...e})}export{D,y as a,N as b,w as c,b as d,C as e,z as f,h as g};
