import{m as n,j as e,L as d}from"./app-FvBJISK9.js";import{I as c}from"./input-error-DrRNtEt0.js";import{T as p}from"./text-link-CZpTP5yq.js";import{B as x}from"./app-logo-icon-CT9-NZ3f.js";import{I as u}from"./input-VMGj1HUy.js";import{L as f}from"./label-BxUs1jY6.js";import{A as j,L as h}from"./auth-layout-Cp79PbjC.js";import"./index-DBDRXzgx.js";function F({status:r}){const{data:a,setData:i,post:o,processing:t,errors:m}=n({email:""}),l=s=>{s.preventDefault(),o(route("password.email"))};return e.jsxs(j,{title:"Forgot password",description:"Enter your email to receive a password reset link",children:[e.jsx(d,{title:"Forgot password"}),r&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:r}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("form",{onSubmit:l,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(f,{htmlFor:"email",children:"Email address"}),e.jsx(u,{id:"email",type:"email",name:"email",autoComplete:"off",value:a.email,autoFocus:!0,onChange:s=>i("email",s.target.value),placeholder:"<EMAIL>"}),e.jsx(c,{message:m.email})]}),e.jsx("div",{className:"my-6 flex items-center justify-start",children:e.jsxs(x,{className:"w-full",disabled:t,children:[t&&e.jsx(h,{className:"h-4 w-4 animate-spin"}),"Email password reset link"]})})]}),e.jsxs("div",{className:"space-x-1 text-center text-sm text-muted-foreground",children:[e.jsx("span",{children:"Or, return to"}),e.jsx(p,{href:route("login"),children:"log in"})]})]})]})}export{F as default};
