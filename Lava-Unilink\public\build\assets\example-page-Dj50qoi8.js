import{j as e,L as a}from"./app-CtrL47ne.js";import{A as s}from"./app-layout-ac8Ndnxx.js";import{T as r}from"./three-column-layout-CcmqGlmU.js";import"./app-logo-icon-CQl1RRw8.js";import"./index-C70TSJlT.js";import"./index-BIL34fKe.js";import"./book-open-bnIOiasB.js";import"./message-circle-BJ_C4hpy.js";const t=[{title:"Example Page",href:"/example"}];function h(){return e.jsxs(s,{breadcrumbs:t,children:[e.jsx(a,{title:"Example Page"}),e.jsx(r,{children:e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsx("h4",{className:"mb-0",children:"Example with Default Sidebars"})}),e.jsxs("div",{className:"card-body",children:[e.jsx("p",{children:"This page uses the default left and right sidebars from the ThreeColumnLayout component."}),e.jsx("p",{children:"The layout automatically provides consistent navigation and quick links."})]})]})})]})}export{h as default};
