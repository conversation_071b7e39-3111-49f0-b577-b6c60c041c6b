import{r as i,j as e,L as C,$ as d}from"./app-FHRWCuO9.js";import{A as S,b as x,c as u,e as h,U as p,d as z,g as _,B as g}from"./app-layout-CQXuNuUM.js";import{B as n}from"./app-logo-icon-CDT9SgfN.js";import{I as A}from"./input-B0Iek5Am.js";import{P as O}from"./plus-BhmGpGeU.js";import{S as $}from"./search-Pumo25UI.js";import{F as L}from"./file-text-DibjYX7M.js";import"./index-CZG6ZiXE.js";import"./index-BJKo6RB-.js";function H(){const[t,j]=i.useState(null),[v,o]=i.useState(!0),[l,y]=i.useState(""),[a,m]=i.useState({type:"",campus:"",status:"active"}),c=async(s=1)=>{o(!0);try{const r=new URLSearchParams({page:s.toString(),per_page:"12",...l&&{search:l},...a.type&&{type:a.type},...a.campus&&{campus:a.campus},...a.status&&{status:a.status}}),w=await(await fetch(`/api/v1/organizations?${r}`)).json();j(w)}catch(r){console.error("Error fetching organizations:",r)}finally{o(!1)}};i.useEffect(()=>{c()},[l,a]);const f=s=>{s.preventDefault(),c()},N=s=>{switch(s){case"active":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"inactive":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}},b=s=>{switch(s.toLowerCase()){case"club":return"bg-blue-100 text-blue-800";case"society":return"bg-purple-100 text-purple-800";case"department":return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}};return e.jsxs(S,{children:[e.jsx(C,{title:"Organizations"}),e.jsx("div",{className:"py-12",children:e.jsx("div",{className:"max-w-7xl mx-auto sm:px-6 lg:px-8",children:e.jsx("div",{className:"bg-white overflow-hidden shadow-sm sm:rounded-lg",children:e.jsxs("div",{className:"p-6 text-gray-900",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Organizations"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Discover and join university organizations"})]}),e.jsx(d,{href:"/organizations/create",children:e.jsxs(n,{children:[e.jsx(O,{className:"w-4 h-4 mr-2"}),"Create Organization"]})})]}),e.jsxs("div",{className:"mb-6 space-y-4",children:[e.jsxs("form",{onSubmit:f,className:"flex gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx($,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx(A,{type:"text",placeholder:"Search organizations...",value:l,onChange:s=>y(s.target.value),className:"pl-10"})]})}),e.jsx(n,{type:"submit",children:"Search"})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("select",{value:a.type,onChange:s=>m({...a,type:s.target.value}),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"club",children:"Club"}),e.jsx("option",{value:"society",children:"Society"}),e.jsx("option",{value:"department",children:"Department"})]}),e.jsxs("select",{value:a.status,onChange:s=>m({...a,status:s.target.value}),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"inactive",children:"Inactive"})]})]})]}),v?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((s,r)=>e.jsxs(x,{className:"animate-pulse",children:[e.jsxs(u,{children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]}),e.jsxs(h,{children:[e.jsx("div",{className:"h-3 bg-gray-200 rounded w-full mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})]},r))}):(t==null?void 0:t.data.length)===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx(p,{className:"w-16 h-16 mx-auto"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No organizations found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search criteria or create a new organization."}),e.jsx(d,{href:"/organizations/create",children:e.jsx(n,{children:"Create Organization"})})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t==null?void 0:t.data.map(s=>e.jsx(x,{className:"hover:shadow-lg transition-shadow cursor-pointer",children:e.jsxs(d,{href:`/organizations/${s.id}`,children:[e.jsx(u,{children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(z,{className:"text-lg",children:s.name}),e.jsxs(_,{className:"mt-1",children:[s.campus&&`${s.campus} • `,new Date(s.created_at).getFullYear()]})]}),s.logo&&e.jsx("img",{src:`/storage/${s.logo}`,alt:`${s.name} logo`,className:"w-12 h-12 rounded-lg object-cover"})]})}),e.jsxs(h,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4 line-clamp-2",children:s.description||"No description available."}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx(g,{className:b(s.type),children:s.type}),e.jsx(g,{className:N(s.status),children:s.status})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(p,{className:"w-4 h-4 mr-1"}),s.members_count," members"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{className:"w-4 h-4 mr-1"}),s.posts_count," posts"]})]})]})]})},s.id))}),t&&t.last_page>1&&e.jsx("div",{className:"mt-8 flex justify-center",children:e.jsx("div",{className:"flex gap-2",children:Array.from({length:t.last_page},(s,r)=>r+1).map(s=>e.jsx(n,{variant:s===t.current_page?"default":"outline",size:"sm",onClick:()=>c(s),children:s},s))})})]})})})})]})}export{H as default};
