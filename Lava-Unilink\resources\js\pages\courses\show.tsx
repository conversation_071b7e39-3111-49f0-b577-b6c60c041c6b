import { useState, useEffect } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    ArrowLeft,
    BookOpen,
    Users,
    Clock,
    Star,
    Calendar,
    MapPin,
    Download,
    MessageCircle,
    FileText,
    Video,
    Link as LinkIcon
} from 'lucide-react';

interface Course {
    id: number;
    title: string;
    description: string;
    code: string;
    credits: number;
    semester: string;
    year: string;
    instructor: {
        id: number;
        name: string;
        email: string;
        avatar?: string;
        bio?: string;
    };
    department: string;
    campus: string;
    schedule: string;
    room: string;
    enrolled_count: number;
    max_capacity: number;
    status: 'active' | 'inactive' | 'full';
    rating: number;
    is_enrolled: boolean;
    syllabus?: string;
    materials: Array<{
        id: number;
        title: string;
        type: 'document' | 'video' | 'link';
        url: string;
        size?: string;
        uploaded_at: string;
    }>;
    announcements: Array<{
        id: number;
        title: string;
        content: string;
        created_at: string;
    }>;
    created_at: string;
}

interface CourseShowProps {
    courseId: string;
    course?: Course;
}

export default function CourseShow({ courseId, course: initialCourse }: CourseShowProps) {
    const [course, setCourse] = useState<Course | null>(initialCourse || null);
    const [loading, setLoading] = useState(!initialCourse);
    const [error, setError] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState('overview');

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Courses',
            href: '/courses',
        },
        {
            title: course?.title || 'Course',
            href: `/courses/${courseId}`,
        },
    ];

    // Load course if not provided
    useEffect(() => {
        if (!initialCourse) {
            loadCourse();
        }
    }, [courseId]);

    const loadCourse = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await fetch(`/api/v1/courses/${courseId}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                setCourse(data);
            } else if (response.status === 404) {
                setError('Course not found');
            } else {
                setError('Failed to load course');
            }
        } catch (error) {
            console.error('Error loading course:', error);
            setError('An unexpected error occurred');
        } finally {
            setLoading(false);
        }
    };

    const handleEnroll = async () => {
        if (!course) return;

        try {
            const response = await fetch(`/api/v1/courses/${course.id}/enroll`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (response.ok) {
                setCourse(prev => prev ? { ...prev, is_enrolled: true, enrolled_count: prev.enrolled_count + 1 } : null);
            } else {
                alert('Failed to enroll in course');
            }
        } catch (error) {
            console.error('Error enrolling in course:', error);
            alert('An error occurred while enrolling');
        }
    };

    const getStatusBadge = (course: Course) => {
        if (course.status === 'full') {
            return <span className="badge bg-danger">Full</span>;
        }
        if (course.status === 'inactive') {
            return <span className="badge bg-secondary">Inactive</span>;
        }
        if (course.is_enrolled) {
            return <span className="badge bg-success">Enrolled</span>;
        }
        return <span className="badge bg-primary">Available</span>;
    };

    const getMaterialIcon = (type: string) => {
        switch (type) {
            case 'document': return <FileText className="w-4 h-4" />;
            case 'video': return <Video className="w-4 h-4" />;
            case 'link': return <LinkIcon className="w-4 h-4" />;
            default: return <FileText className="w-4 h-4" />;
        }
    };

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Loading..." />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="text-center py-5">
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <p className="mt-3 text-muted">Loading course...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (error || !course) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Error" />
                <div className="main-container">
                    <div className="content-area">
                        <div className="container mt-4">
                            <div className="card shadow-sm">
                                <div className="card-body text-center py-5">
                                    <h3 className="h5 mb-3">Course Not Found</h3>
                                    <p className="text-muted mb-4">
                                        {error || 'The course you are looking for does not exist.'}
                                    </p>
                                    <Link href="/courses" className="btn btn-primary">
                                        <ArrowLeft className="w-4 h-4 me-2" />
                                        Back to Courses
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={course.title} />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        {/* Header */}
                        <div className="d-flex align-items-center justify-content-between mb-4">
                            <Link href="/courses" className="btn btn-outline-secondary">
                                <ArrowLeft className="w-4 h-4 me-2" />
                                Back to Courses
                            </Link>
                            
                            {course.is_enrolled ? (
                                <div className="d-flex gap-2">
                                    <Link href={`/courses/${course.id}/discussions`} className="btn btn-outline-primary">
                                        <MessageCircle className="w-4 h-4 me-2" />
                                        Discussions
                                    </Link>
                                    {course.syllabus && (
                                        <a href={course.syllabus} className="btn btn-outline-secondary" target="_blank">
                                            <Download className="w-4 h-4 me-2" />
                                            Syllabus
                                        </a>
                                    )}
                                </div>
                            ) : course.status === 'active' && course.status !== 'full' && (
                                <button onClick={handleEnroll} className="btn btn-primary">
                                    Enroll Now
                                </button>
                            )}
                        </div>

                        <div className="row">
                            {/* Main Content */}
                            <div className="col-md-8">
                                {/* Course Header */}
                                <div className="card shadow-sm mb-4">
                                    <div className="card-body">
                                        <div className="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <h1 className="h3 mb-2">{course.title}</h1>
                                                <p className="text-muted mb-0">{course.code} • {course.credits} Credits</p>
                                            </div>
                                            {getStatusBadge(course)}
                                        </div>

                                        <div className="row g-3 mb-3">
                                            <div className="col-md-6">
                                                <div className="d-flex align-items-center text-muted">
                                                    <Calendar className="w-4 h-4 me-2" />
                                                    <span>{course.semester} {course.year}</span>
                                                </div>
                                            </div>
                                            <div className="col-md-6">
                                                <div className="d-flex align-items-center text-muted">
                                                    <Clock className="w-4 h-4 me-2" />
                                                    <span>{course.schedule}</span>
                                                </div>
                                            </div>
                                            <div className="col-md-6">
                                                <div className="d-flex align-items-center text-muted">
                                                    <MapPin className="w-4 h-4 me-2" />
                                                    <span>{course.room} • {course.campus}</span>
                                                </div>
                                            </div>
                                            <div className="col-md-6">
                                                <div className="d-flex align-items-center text-muted">
                                                    <Users className="w-4 h-4 me-2" />
                                                    <span>{course.enrolled_count}/{course.max_capacity} enrolled</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="d-flex align-items-center">
                                            <Star className="w-4 h-4 text-warning me-1" />
                                            <span className="me-3">{course.rating.toFixed(1)} rating</span>
                                            <span className="badge bg-light text-dark">{course.department}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Tabs */}
                                <div className="card shadow-sm">
                                    <div className="card-header">
                                        <ul className="nav nav-tabs card-header-tabs">
                                            <li className="nav-item">
                                                <button
                                                    className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                                                    onClick={() => setActiveTab('overview')}
                                                >
                                                    Overview
                                                </button>
                                            </li>
                                            {course.is_enrolled && (
                                                <>
                                                    <li className="nav-item">
                                                        <button
                                                            className={`nav-link ${activeTab === 'materials' ? 'active' : ''}`}
                                                            onClick={() => setActiveTab('materials')}
                                                        >
                                                            Materials
                                                        </button>
                                                    </li>
                                                    <li className="nav-item">
                                                        <button
                                                            className={`nav-link ${activeTab === 'announcements' ? 'active' : ''}`}
                                                            onClick={() => setActiveTab('announcements')}
                                                        >
                                                            Announcements
                                                        </button>
                                                    </li>
                                                </>
                                            )}
                                        </ul>
                                    </div>
                                    <div className="card-body">
                                        {activeTab === 'overview' && (
                                            <div>
                                                <h5>Course Description</h5>
                                                <p className="text-muted">{course.description}</p>
                                            </div>
                                        )}

                                        {activeTab === 'materials' && course.is_enrolled && (
                                            <div>
                                                <h5 className="mb-3">Course Materials</h5>
                                                {course.materials.length > 0 ? (
                                                    <div className="list-group">
                                                        {course.materials.map((material) => (
                                                            <a
                                                                key={material.id}
                                                                href={material.url}
                                                                className="list-group-item list-group-item-action d-flex align-items-center"
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                            >
                                                                {getMaterialIcon(material.type)}
                                                                <div className="ms-3 flex-grow-1">
                                                                    <h6 className="mb-1">{material.title}</h6>
                                                                    <small className="text-muted">
                                                                        {material.size && `${material.size} • `}
                                                                        Uploaded {new Date(material.uploaded_at).toLocaleDateString()}
                                                                    </small>
                                                                </div>
                                                                <Download className="w-4 h-4 text-muted" />
                                                            </a>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <p className="text-muted">No materials available yet.</p>
                                                )}
                                            </div>
                                        )}

                                        {activeTab === 'announcements' && course.is_enrolled && (
                                            <div>
                                                <h5 className="mb-3">Announcements</h5>
                                                {course.announcements.length > 0 ? (
                                                    <div className="space-y-3">
                                                        {course.announcements.map((announcement) => (
                                                            <div key={announcement.id} className="border rounded p-3 mb-3">
                                                                <h6 className="mb-2">{announcement.title}</h6>
                                                                <p className="text-muted mb-2">{announcement.content}</p>
                                                                <small className="text-muted">
                                                                    {new Date(announcement.created_at).toLocaleDateString()}
                                                                </small>
                                                            </div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <p className="text-muted">No announcements yet.</p>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Sidebar */}
                            <div className="col-md-4">
                                {/* Instructor Info */}
                                <div className="card shadow-sm mb-4">
                                    <div className="card-header">
                                        <h6 className="mb-0">Instructor</h6>
                                    </div>
                                    <div className="card-body">
                                        <div className="d-flex align-items-center mb-3">
                                            <img
                                                src={course.instructor.avatar || '/img/profilepic.jpg'}
                                                alt={course.instructor.name}
                                                className="rounded-circle me-3"
                                                width="50"
                                                height="50"
                                                style={{objectFit: 'cover'}}
                                            />
                                            <div>
                                                <h6 className="mb-0">{course.instructor.name}</h6>
                                                <small className="text-muted">{course.instructor.email}</small>
                                            </div>
                                        </div>
                                        {course.instructor.bio && (
                                            <p className="small text-muted mb-0">{course.instructor.bio}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Quick Actions */}
                                {course.is_enrolled && (
                                    <div className="card shadow-sm">
                                        <div className="card-header">
                                            <h6 className="mb-0">Quick Actions</h6>
                                        </div>
                                        <div className="list-group list-group-flush">
                                            <Link href={`/courses/${course.id}/discussions`} className="list-group-item list-group-item-action">
                                                <MessageCircle className="w-4 h-4 me-2" />
                                                Course Discussions
                                            </Link>
                                            <Link href={`/courses/${course.id}/assignments`} className="list-group-item list-group-item-action">
                                                <FileText className="w-4 h-4 me-2" />
                                                Assignments
                                            </Link>
                                            <Link href={`/courses/${course.id}/grades`} className="list-group-item list-group-item-action">
                                                <Star className="w-4 h-4 me-2" />
                                                Grades
                                            </Link>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
