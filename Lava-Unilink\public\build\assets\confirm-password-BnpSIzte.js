import{m as d,j as s,L as m}from"./app-C5rOGN_d.js";import{I as l}from"./input-error-DCsCfINM.js";import{B as c}from"./app-logo-icon-DIRi9GnE.js";import{I as u}from"./input-CW4FxrAw.js";import{L as f}from"./label-CXIa3m3Q.js";import{A as w,L as h}from"./auth-layout-C1VSUca9.js";import"./index-C4DufK0h.js";function y(){const{data:o,setData:e,post:t,processing:a,errors:i,reset:n}=d({password:""}),p=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>n("password")})};return s.jsxs(w,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(m,{title:"Confirm password"}),s.jsx("form",{onSubmit:p,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(f,{htmlFor:"password",children:"Password"}),s.jsx(u,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:o.password,autoFocus:!0,onChange:r=>e("password",r.target.value)}),s.jsx(l,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(c,{className:"w-full",disabled:a,children:[a&&s.jsx(h,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{y as default};
