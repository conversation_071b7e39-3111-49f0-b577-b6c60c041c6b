<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\Organization;
use App\Events\PostCreated;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class PostController extends Controller
{
    /**
     * Display a listing of posts with advanced filtering and search.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Post::with(['user', 'organization', 'reactions.user'])
                    ->withCount(['comments', 'reactions']);

        // Apply visibility filters based on user permissions
        $user = Auth::user();
        $organizationIds = $user->organizations()->where('status', 'active')->pluck('id')->toArray();

        $query->where(function ($q) use ($organizationIds, $user) {
            $q->where('visibility', 'public')
              ->orWhere(function ($subQ) use ($organizationIds) {
                  $subQ->where('visibility', 'members_only')
                       ->whereIn('organization_id', $organizationIds);
              })
              ->orWhere('user_id', $user->id);
        });

        // Filter by organization
        if ($request->filled('organization_id')) {
            $query->where('organization_id', $request->organization_id);
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by campus
        if ($request->filled('campus')) {
            $query->whereHas('organization', function ($q) use ($request) {
                $q->where('campus', $request->campus);
            });
        }

        // Only show published posts unless user is admin or post owner
        if (!$user->isAdmin()) {
            $query->published();
        }

        // Enhanced search in title, content, user name, and organization name
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('content', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('user', function ($userQ) use ($searchTerm) {
                      $userQ->where('name', 'LIKE', "%{$searchTerm}%");
                  })
                  ->orWhereHas('organization', function ($orgQ) use ($searchTerm) {
                      $orgQ->where('name', 'LIKE', "%{$searchTerm}%");
                  });
            });
        }

        // Date range filtering
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if ($sortBy === 'popularity') {
            $query->orderByDesc('reactions_count')
                  ->orderByDesc('comments_count')
                  ->orderByDesc('created_at');
        } elseif ($sortBy === 'trending') {
            // Trending algorithm: recent posts with high engagement
            $query->selectRaw('posts.*,
                (reactions_count + comments_count * 2) /
                (DATEDIFF(NOW(), posts.created_at) + 1) as trending_score')
                  ->orderByDesc('trending_score');
        } else {
            $query->orderBy('is_pinned', 'desc')
                  ->orderBy($sortBy, $sortOrder);
        }

        $posts = $query->paginate($request->get('per_page', 15));

        // Add user reaction status for each post
        foreach ($posts as $post) {
            $post->user_reaction = $post->reactions()
                                       ->where('user_id', $user->id)
                                       ->first()?->type;
        }

        return response()->json($posts);
    }

    /**
     * Store a newly created post.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|string|in:announcement,discussion,event,news',
            'media' => 'nullable|array',
            'media.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240',
            'visibility' => 'required|string|in:public,members_only,private',
            'organization_id' => 'nullable|exists:organizations,id',
            'is_pinned' => 'boolean',
            'comments_enabled' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // Check if user can post to organization
        if (isset($validated['organization_id']) && $validated['organization_id']) {
            $organization = Organization::find($validated['organization_id']);
            if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'You are not a member of this organization'], 403);
            }
        }

        // Handle media uploads
        if ($request->hasFile('media')) {
            $mediaFiles = [];
            foreach ($request->file('media') as $file) {
                $path = $file->store('posts/media', 'public');
                $mediaFiles[] = [
                    'path' => $path,
                    'name' => $file->getClientOriginalName(),
                    'type' => $file->getMimeType(),
                    'size' => $file->getSize(),
                ];
            }
            $validated['media'] = $mediaFiles;
        }

        $validated['user_id'] = Auth::id();

        // Set published_at to now if not provided and not a draft
        if (!isset($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $post = Post::create($validated);
        $post->load(['user', 'organization', 'reactions']);

        // Fire the PostCreated event for real-time updates
        event(new PostCreated($post));

        return response()->json([
            'message' => 'Post created successfully',
            'post' => $post
        ], 201);
    }

    /**
     * Display the specified post.
     */
    public function show(Post $post): JsonResponse
    {
        // Check visibility permissions
        if ($post->visibility === 'private' && $post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Post not found'], 404);
        }

        if ($post->visibility === 'members_only' && $post->organization_id) {
            $organization = $post->organization;
            if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'Post not found'], 404);
            }
        }

        $post->load([
            'user',
            'organization',
            'comments' => function ($query) {
                $query->topLevel()->with(['user', 'replies.user', 'reactions'])->latest();
            },
            'reactions.user'
        ]);

        $post->loadCount(['comments', 'reactions']);

        return response()->json($post);
    }

    /**
     * Update the specified post.
     */
    public function update(Request $request, Post $post): JsonResponse
    {
        // Check if user can update this post
        if ($post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            // Check if user is admin of the organization
            if ($post->organization_id) {
                $organization = $post->organization;
                if (!$organization->hasAdmin(Auth::user())) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            } else {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|string|in:announcement,discussion,event,news',
            'media' => 'nullable|array',
            'media.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240',
            'visibility' => 'required|string|in:public,members_only,private',
            'is_pinned' => 'boolean',
            'comments_enabled' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // Handle media uploads
        if ($request->hasFile('media')) {
            // Delete old media files
            if ($post->media) {
                foreach ($post->media as $media) {
                    Storage::disk('public')->delete($media['path']);
                }
            }

            $mediaFiles = [];
            foreach ($request->file('media') as $file) {
                $path = $file->store('posts/media', 'public');
                $mediaFiles[] = [
                    'path' => $path,
                    'name' => $file->getClientOriginalName(),
                    'type' => $file->getMimeType(),
                    'size' => $file->getSize(),
                ];
            }
            $validated['media'] = $mediaFiles;
        }

        $post->update($validated);

        return response()->json([
            'message' => 'Post updated successfully',
            'post' => $post->load(['user', 'organization', 'reactions'])
        ]);
    }

    /**
     * Remove the specified post.
     */
    public function destroy(Post $post): JsonResponse
    {
        // Check if user can delete this post
        if ($post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            // Check if user is admin of the organization
            if ($post->organization_id) {
                $organization = $post->organization;
                if (!$organization->hasAdmin(Auth::user())) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            } else {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        // Delete associated media files
        if ($post->media) {
            foreach ($post->media as $media) {
                Storage::disk('public')->delete($media['path']);
            }
        }

        $post->delete();

        return response()->json(['message' => 'Post deleted successfully']);
    }

    /**
     * Get personalized feed for the authenticated user.
     */
    public function feed(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            $organizationIds = $user->organizations()->where('status', 'active')->pluck('id')->toArray();

        $query = Post::with(['user', 'organization', 'reactions.user'])
                    ->withCount(['comments', 'reactions'])
                    ->published();

        // Personalized feed logic
        $query->where(function ($q) use ($organizationIds, $user) {
            // Posts from user's organizations (higher priority)
            $q->whereIn('organization_id', $organizationIds)
              // Public posts from same campus
              ->orWhere(function ($campusQ) use ($user) {
                  $campusQ->where('visibility', 'public')
                          ->whereHas('organization', function ($orgQ) use ($user) {
                              $orgQ->where('campus', $user->campus);
                          });
              })
              // User's own posts
              ->orWhere('user_id', $user->id)
              // Trending public posts
              ->orWhere(function ($trendingQ) {
                  $trendingQ->where('visibility', 'public')
                           ->where('created_at', '>=', now()->subDays(7))
                           ->havingRaw('(reactions_count + comments_count) > 5');
              });
        });

        // Apply filters if provided
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Personalized sorting: prioritize user's organizations and recent activity
        $orgIds = empty($organizationIds) ? [0] : array_merge($organizationIds, [0]);
        $query->selectRaw('posts.*,
            CASE
                WHEN organization_id IN (' . implode(',', $orgIds) . ') THEN 3
                WHEN visibility = "public" AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 2
                ELSE 1
            END as priority_score')
              ->orderByDesc('priority_score')
              ->orderByDesc('created_at');

        $posts = $query->paginate($request->get('per_page', 15));

        // Add user reaction status for each post
        foreach ($posts as $post) {
            $post->user_reaction = $post->reactions()
                                       ->where('user_id', $user->id)
                                       ->first()?->type;
        }

        return response()->json($posts);
        } catch (\Exception $e) {
            \Log::error('Feed API Error: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Get trending posts.
     */
    public function trending(Request $request): JsonResponse
    {
        $user = Auth::user();
        $timeframe = $request->get('timeframe', 'week'); // day, week, month

        $dateFilter = match($timeframe) {
            'day' => now()->subDay(),
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            default => now()->subWeek()
        };

        $query = Post::with(['user', 'organization', 'reactions.user'])
                    ->withCount(['comments', 'reactions'])
                    ->published()
                    ->where('created_at', '>=', $dateFilter)
                    ->where('visibility', 'public');

        // Calculate trending score
        $query->selectRaw('posts.*,
            (reactions_count * 1.5 + comments_count * 2) /
            (DATEDIFF(NOW(), posts.created_at) + 1) as trending_score')
              ->having('trending_score', '>', 0.5)
              ->orderByDesc('trending_score');

        $posts = $query->paginate($request->get('per_page', 15));

        // Add user reaction status for each post
        foreach ($posts as $post) {
            $post->user_reaction = $post->reactions()
                                       ->where('user_id', $user->id)
                                       ->first()?->type;
        }

        return response()->json($posts);
    }

    /**
     * Get posts by organization with member-specific content.
     */
    public function organizationFeed(Request $request, Organization $organization): JsonResponse
    {
        $user = Auth::user();

        // Check if user can view organization posts
        if (!$organization->hasMember($user) && !$user->isAdmin()) {
            return response()->json(['message' => 'Access denied'], 403);
        }

        $query = Post::with(['user', 'organization', 'reactions.user'])
                    ->withCount(['comments', 'reactions'])
                    ->where('organization_id', $organization->id)
                    ->published();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $query->orderBy('is_pinned', 'desc')
              ->orderBy('created_at', 'desc');

        $posts = $query->paginate($request->get('per_page', 15));

        // Add user reaction status for each post
        foreach ($posts as $post) {
            $post->user_reaction = $post->reactions()
                                       ->where('user_id', $user->id)
                                       ->first()?->type;
        }

        return response()->json($posts);
    }

    /**
     * Get feed statistics for analytics.
     */
    public function feedStats(Request $request): JsonResponse
    {
        $user = Auth::user();
        $organizationIds = $user->organizations()->where('status', 'active')->pluck('id')->toArray();

        $stats = [
            'total_posts' => Post::published()->count(),
            'user_posts' => Post::where('user_id', $user->id)->count(),
            'organization_posts' => Post::whereIn('organization_id', $organizationIds)->count(),
            'trending_posts' => Post::where('created_at', '>=', now()->subWeek())
                                   ->havingRaw('(reactions_count + comments_count) > 5')
                                   ->count(),
            'post_types' => Post::published()
                               ->selectRaw('type, count(*) as count')
                               ->groupBy('type')
                               ->pluck('count', 'type')
                               ->toArray(),
        ];

        return response()->json($stats);
    }
}
