import{r as c,j as e,L as u,$ as l}from"./app-CSPm8fiq.js";import{A as j,D as g,C as L,a as E,U as F}from"./app-layout-CtjvFf2e.js";import{A as f}from"./arrow-left-Bldo8gx-.js";import{M as w}from"./message-circle-Dp-T4UGq.js";import{M as D}from"./map-pin-DvJfsFwR.js";import{S as y}from"./star-EeZt1xwZ.js";import{F as N}from"./file-text-D1E5S3jP.js";import{a as k}from"./app-logo-icon-RBJwxtah.js";import"./index-Jg6R2Vip.js";import"./index-B4tLxb2k.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]],T=k("Link",M);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]],B=k("Video",q);function X({courseId:d,course:o}){const[s,p]=c.useState(o||null),[C,v]=c.useState(!o),[b,i]=c.useState(null),[t,m]=c.useState("overview"),h=[{title:"Courses",href:"/courses"},{title:(s==null?void 0:s.title)||"Course",href:`/courses/${d}`}];c.useEffect(()=>{o||_()},[d]);const _=async()=>{var a;v(!0),i(null);try{const r=await fetch(`/api/v1/courses/${d}`,{headers:{Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,Accept:"application/json"}});if(r.ok){const n=await r.json();p(n)}else r.status===404?i("Course not found"):i("Failed to load course")}catch(r){console.error("Error loading course:",r),i("An unexpected error occurred")}finally{v(!1)}},A=async()=>{var a,r;if(s)try{(await fetch(`/api/v1/courses/${s.id}/enroll`,{method:"POST",headers:{Authorization:`Bearer ${(a=document.querySelector('meta[name="api-token"]'))==null?void 0:a.getAttribute("content")}`,"X-CSRF-TOKEN":((r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content"))||""}})).ok?p(x=>x?{...x,is_enrolled:!0,enrolled_count:x.enrolled_count+1}:null):alert("Failed to enroll in course")}catch(n){console.error("Error enrolling in course:",n),alert("An error occurred while enrolling")}},S=a=>a.status==="full"?e.jsx("span",{className:"badge bg-danger",children:"Full"}):a.status==="inactive"?e.jsx("span",{className:"badge bg-secondary",children:"Inactive"}):a.is_enrolled?e.jsx("span",{className:"badge bg-success",children:"Enrolled"}):e.jsx("span",{className:"badge bg-primary",children:"Available"}),$=a=>{switch(a){case"document":return e.jsx(N,{className:"w-4 h-4"});case"video":return e.jsx(B,{className:"w-4 h-4"});case"link":return e.jsx(T,{className:"w-4 h-4"});default:return e.jsx(N,{className:"w-4 h-4"})}};return C?e.jsxs(j,{breadcrumbs:h,children:[e.jsx(u,{title:"Loading..."}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsxs("div",{className:"text-center py-5",children:[e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"mt-3 text-muted",children:"Loading course..."})]})})})})]}):b||!s?e.jsxs(j,{breadcrumbs:h,children:[e.jsx(u,{title:"Error"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"card shadow-sm",children:e.jsxs("div",{className:"card-body text-center py-5",children:[e.jsx("h3",{className:"h5 mb-3",children:"Course Not Found"}),e.jsx("p",{className:"text-muted mb-4",children:b||"The course you are looking for does not exist."}),e.jsxs(l,{href:"/courses",className:"btn btn-primary",children:[e.jsx(f,{className:"w-4 h-4 me-2"}),"Back to Courses"]})]})})})})})]}):e.jsxs(j,{breadcrumbs:h,children:[e.jsx(u,{title:s.title}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsxs("div",{className:"container mt-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsxs(l,{href:"/courses",className:"btn btn-outline-secondary",children:[e.jsx(f,{className:"w-4 h-4 me-2"}),"Back to Courses"]}),s.is_enrolled?e.jsxs("div",{className:"d-flex gap-2",children:[e.jsxs(l,{href:`/courses/${s.id}/discussions`,className:"btn btn-outline-primary",children:[e.jsx(w,{className:"w-4 h-4 me-2"}),"Discussions"]}),s.syllabus&&e.jsxs("a",{href:s.syllabus,className:"btn btn-outline-secondary",target:"_blank",children:[e.jsx(g,{className:"w-4 h-4 me-2"}),"Syllabus"]})]}):s.status==="active"&&s.status!=="full"&&e.jsx("button",{onClick:A,className:"btn btn-primary",children:"Enroll Now"})]}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-8",children:[e.jsx("div",{className:"card shadow-sm mb-4",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-start mb-3",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"h3 mb-2",children:s.title}),e.jsxs("p",{className:"text-muted mb-0",children:[s.code," • ",s.credits," Credits"]})]}),S(s)]}),e.jsxs("div",{className:"row g-3 mb-3",children:[e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"d-flex align-items-center text-muted",children:[e.jsx(L,{className:"w-4 h-4 me-2"}),e.jsxs("span",{children:[s.semester," ",s.year]})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"d-flex align-items-center text-muted",children:[e.jsx(E,{className:"w-4 h-4 me-2"}),e.jsx("span",{children:s.schedule})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"d-flex align-items-center text-muted",children:[e.jsx(D,{className:"w-4 h-4 me-2"}),e.jsxs("span",{children:[s.room," • ",s.campus]})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"d-flex align-items-center text-muted",children:[e.jsx(F,{className:"w-4 h-4 me-2"}),e.jsxs("span",{children:[s.enrolled_count,"/",s.max_capacity," enrolled"]})]})})]}),e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(y,{className:"w-4 h-4 text-warning me-1"}),e.jsxs("span",{className:"me-3",children:[s.rating.toFixed(1)," rating"]}),e.jsx("span",{className:"badge bg-light text-dark",children:s.department})]})]})}),e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsxs("ul",{className:"nav nav-tabs card-header-tabs",children:[e.jsx("li",{className:"nav-item",children:e.jsx("button",{className:`nav-link ${t==="overview"?"active":""}`,onClick:()=>m("overview"),children:"Overview"})}),s.is_enrolled&&e.jsxs(e.Fragment,{children:[e.jsx("li",{className:"nav-item",children:e.jsx("button",{className:`nav-link ${t==="materials"?"active":""}`,onClick:()=>m("materials"),children:"Materials"})}),e.jsx("li",{className:"nav-item",children:e.jsx("button",{className:`nav-link ${t==="announcements"?"active":""}`,onClick:()=>m("announcements"),children:"Announcements"})})]})]})}),e.jsxs("div",{className:"card-body",children:[t==="overview"&&e.jsxs("div",{children:[e.jsx("h5",{children:"Course Description"}),e.jsx("p",{className:"text-muted",children:s.description})]}),t==="materials"&&s.is_enrolled&&e.jsxs("div",{children:[e.jsx("h5",{className:"mb-3",children:"Course Materials"}),s.materials.length>0?e.jsx("div",{className:"list-group",children:s.materials.map(a=>e.jsxs("a",{href:a.url,className:"list-group-item list-group-item-action d-flex align-items-center",target:"_blank",rel:"noopener noreferrer",children:[$(a.type),e.jsxs("div",{className:"ms-3 flex-grow-1",children:[e.jsx("h6",{className:"mb-1",children:a.title}),e.jsxs("small",{className:"text-muted",children:[a.size&&`${a.size} • `,"Uploaded ",new Date(a.uploaded_at).toLocaleDateString()]})]}),e.jsx(g,{className:"w-4 h-4 text-muted"})]},a.id))}):e.jsx("p",{className:"text-muted",children:"No materials available yet."})]}),t==="announcements"&&s.is_enrolled&&e.jsxs("div",{children:[e.jsx("h5",{className:"mb-3",children:"Announcements"}),s.announcements.length>0?e.jsx("div",{className:"space-y-3",children:s.announcements.map(a=>e.jsxs("div",{className:"border rounded p-3 mb-3",children:[e.jsx("h6",{className:"mb-2",children:a.title}),e.jsx("p",{className:"text-muted mb-2",children:a.content}),e.jsx("small",{className:"text-muted",children:new Date(a.created_at).toLocaleDateString()})]},a.id))}):e.jsx("p",{className:"text-muted",children:"No announcements yet."})]})]})]})]}),e.jsxs("div",{className:"col-md-4",children:[e.jsxs("div",{className:"card shadow-sm mb-4",children:[e.jsx("div",{className:"card-header",children:e.jsx("h6",{className:"mb-0",children:"Instructor"})}),e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"d-flex align-items-center mb-3",children:[e.jsx("img",{src:s.instructor.avatar||"/img/profilepic.jpg",alt:s.instructor.name,className:"rounded-circle me-3",width:"50",height:"50",style:{objectFit:"cover"}}),e.jsxs("div",{children:[e.jsx("h6",{className:"mb-0",children:s.instructor.name}),e.jsx("small",{className:"text-muted",children:s.instructor.email})]})]}),s.instructor.bio&&e.jsx("p",{className:"small text-muted mb-0",children:s.instructor.bio})]})]}),s.is_enrolled&&e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsx("h6",{className:"mb-0",children:"Quick Actions"})}),e.jsxs("div",{className:"list-group list-group-flush",children:[e.jsxs(l,{href:`/courses/${s.id}/discussions`,className:"list-group-item list-group-item-action",children:[e.jsx(w,{className:"w-4 h-4 me-2"}),"Course Discussions"]}),e.jsxs(l,{href:`/courses/${s.id}/assignments`,className:"list-group-item list-group-item-action",children:[e.jsx(N,{className:"w-4 h-4 me-2"}),"Assignments"]}),e.jsxs(l,{href:`/courses/${s.id}/grades`,className:"list-group-item list-group-item-action",children:[e.jsx(y,{className:"w-4 h-4 me-2"}),"Grades"]})]})]})]})]})]})})})]})}export{X as default};
