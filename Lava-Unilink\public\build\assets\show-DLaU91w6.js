import{r as l,j as e,L as g,$ as m}from"./app-CtrL47ne.js";import{A as p,S as T,C as z,U as B}from"./app-layout-ac8Ndnxx.js";import{P as R}from"./post-card-BllH2Q8m.js";import{A as k}from"./arrow-left-DQFbhqAy.js";import{S as D}from"./square-pen-2i0o-tig.js";import{M as U}from"./map-pin-DQGfzEF9.js";import{G as J}from"./globe-CUaCXTEi.js";import{a as K}from"./app-logo-icon-CQl1RRw8.js";import{M as X}from"./message-circle-BJ_C4hpy.js";import{U as G}from"./user-plus-B_oZ5Aq6.js";import{B as W}from"./book-open-bnIOiasB.js";import"./index-C70TSJlT.js";import"./index-BIL34fKe.js";import"./pin-DCoilNIW.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]],H=K("Mail",Y);function me({organizationId:d,organization:u,posts:N}){const[s,j]=l.useState(u||null),[v,f]=l.useState(N||[]),[S,y]=l.useState(!u),[$,w]=l.useState(!1),[_,h]=l.useState(null),[i,x]=l.useState("posts"),b=[{title:"Organizations",href:"/organizations"},{title:(s==null?void 0:s.name)||"Organization",href:`/organizations/${d}`}];l.useEffect(()=>{u||A(),N||O()},[d]);const A=async()=>{var t;y(!0),h(null);try{const a=await fetch(`/api/v1/organizations/${d}`,{headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,Accept:"application/json"}});if(a.ok){const n=await a.json();j(n)}else a.status===404?h("Organization not found"):h("Failed to load organization")}catch(a){console.error("Error loading organization:",a),h("An unexpected error occurred")}finally{y(!1)}},O=async()=>{var t;w(!0);try{const a=await fetch(`/api/v1/organizations/${d}/posts`,{headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,Accept:"application/json"}});if(a.ok){const n=await a.json();f(n.data||[])}}catch(a){console.error("Error loading organization posts:",a)}finally{w(!1)}},E=async()=>{var t,a;if(s)try{const n=await fetch(`/api/v1/organizations/${s.id}/join`,{method:"POST",headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,"X-CSRF-TOKEN":((a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content"))||""}});if(n.ok){const r=await n.json();j(o=>o?{...o,is_member:r.is_member,membership_status:r.membership_status,members_count:r.is_member?o.members_count+1:o.members_count}:null)}else alert("Failed to join organization")}catch(n){console.error("Error joining organization:",n),alert("An error occurred while joining")}},C=async()=>{var t,a;if(!(!s||!confirm("Are you sure you want to leave this organization?")))try{(await fetch(`/api/v1/organizations/${s.id}/leave`,{method:"POST",headers:{Authorization:`Bearer ${(t=document.querySelector('meta[name="api-token"]'))==null?void 0:t.getAttribute("content")}`,"X-CSRF-TOKEN":((a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content"))||""}})).ok?j(r=>r?{...r,is_member:!1,membership_status:void 0,members_count:r.members_count-1}:null):alert("Failed to leave organization")}catch(n){console.error("Error leaving organization:",n),alert("An error occurred while leaving")}},L=async(t,a)=>{var n,r;try{(await fetch("/api/v1/reactions/toggle",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(n=document.querySelector('meta[name="api-token"]'))==null?void 0:n.getAttribute("content")}`,"X-CSRF-TOKEN":((r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content"))||""},body:JSON.stringify({reactable_type:"post",reactable_id:t,type:a})})).ok&&f(F=>F.map(c=>c.id===t?{...c,reactions_count:c.user_reaction===a?c.reactions_count-1:c.reactions_count+(c.user_reaction?0:1),user_reaction:c.user_reaction===a?void 0:a}:c))}catch(o){console.error("Error toggling reaction:",o)}},P=t=>{window.location.href=`/posts/${t}#comments`},M=t=>{var a;(a=navigator.share)==null||a.call(navigator,{title:"UniLink Post",url:`${window.location.origin}/posts/${t}`})},q=t=>{switch(t){case"academic":return"bg-primary";case"cultural":return"bg-success";case"sports":return"bg-warning";case"religious":return"bg-info";case"service":return"bg-secondary";default:return"bg-primary"}};return S?e.jsxs(p,{breadcrumbs:b,children:[e.jsx(g,{title:"Loading..."}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsxs("div",{className:"text-center py-5",children:[e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"mt-3 text-muted",children:"Loading organization..."})]})})})})]}):_||!s?e.jsxs(p,{breadcrumbs:b,children:[e.jsx(g,{title:"Error"}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsx("div",{className:"container mt-4",children:e.jsx("div",{className:"card shadow-sm",children:e.jsxs("div",{className:"card-body text-center py-5",children:[e.jsx("h3",{className:"h5 mb-3",children:"Organization Not Found"}),e.jsx("p",{className:"text-muted mb-4",children:_||"The organization you are looking for does not exist."}),e.jsxs(m,{href:"/organizations",className:"btn btn-primary",children:[e.jsx(k,{className:"w-4 h-4 me-2"}),"Back to Organizations"]})]})})})})})]}):e.jsxs(p,{breadcrumbs:b,children:[e.jsx(g,{title:s.name}),e.jsx("div",{className:"main-container",children:e.jsx("div",{className:"content-area",children:e.jsxs("div",{className:"container mt-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsxs(m,{href:"/organizations",className:"btn btn-outline-secondary",children:[e.jsx(k,{className:"w-4 h-4 me-2"}),"Back to Organizations"]}),s.is_admin&&e.jsxs("div",{className:"d-flex gap-2",children:[e.jsxs(m,{href:`/organizations/${s.id}/edit`,className:"btn btn-outline-primary",children:[e.jsx(D,{className:"w-4 h-4 me-2"}),"Edit"]}),e.jsxs(m,{href:`/organizations/${s.id}/manage`,className:"btn btn-outline-secondary",children:[e.jsx(T,{className:"w-4 h-4 me-2"}),"Manage"]})]})]}),e.jsxs("div",{className:"card shadow-sm mb-4",children:[s.banner&&e.jsx("img",{src:`/storage/${s.banner}`,alt:s.name,className:"card-img-top",style:{height:"200px",objectFit:"cover"}}),e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-md-2 text-center",children:e.jsx("img",{src:s.logo?`/storage/${s.logo}`:"/img/org-placeholder.png",alt:s.name,className:"rounded mb-3",width:"80",height:"80",style:{objectFit:"cover"}})}),e.jsxs("div",{className:"col-md-7",children:[e.jsx("h1",{className:"h3 mb-2",children:s.name}),e.jsxs("div",{className:"d-flex align-items-center mb-2",children:[e.jsx("span",{className:`badge ${q(s.type)} me-2`,children:s.type}),e.jsx("span",{className:"text-muted",children:s.campus})]}),e.jsx("p",{className:"text-muted mb-3",children:s.description}),e.jsxs("div",{className:"row g-2 text-muted small",children:[s.location&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(U,{className:"w-4 h-4 me-1"}),s.location]}),s.established_date&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(z,{className:"w-4 h-4 me-1"}),"Est. ",new Date(s.established_date).getFullYear()]}),s.website&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(J,{className:"w-4 h-4 me-1"}),e.jsx("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"text-decoration-none",children:"Website"})]}),s.email&&e.jsxs("div",{className:"col-md-6",children:[e.jsx(H,{className:"w-4 h-4 me-1"}),e.jsx("a",{href:`mailto:${s.email}`,className:"text-decoration-none",children:s.email})]})]})]}),e.jsx("div",{className:"col-md-3 text-center",children:s.is_member?e.jsxs("div",{className:"d-flex flex-column gap-2",children:[e.jsx("span",{className:"badge bg-success mb-2",children:"Member"}),e.jsx("button",{onClick:C,className:"btn btn-outline-danger btn-sm",children:"Leave Organization"}),e.jsxs(m,{href:`/organizations/${s.id}/discussions`,className:"btn btn-outline-primary btn-sm",children:[e.jsx(X,{className:"w-4 h-4 me-1"}),"Discussions"]})]}):s.membership_status==="pending"?e.jsxs("div",{children:[e.jsx("span",{className:"badge bg-warning mb-2",children:"Pending"}),e.jsx("p",{className:"small text-muted",children:"Your membership request is being reviewed"})]}):e.jsxs("button",{onClick:E,className:"btn btn-primary",children:[e.jsx(G,{className:"w-4 h-4 me-2"}),"Join Organization"]})})]}),e.jsxs("div",{className:"row text-center mt-4 pt-3 border-top",children:[e.jsxs("div",{className:"col-4",children:[e.jsx("div",{className:"h5 mb-0",children:s.members_count}),e.jsx("small",{className:"text-muted",children:"Members"})]}),e.jsxs("div",{className:"col-4",children:[e.jsx("div",{className:"h5 mb-0",children:s.posts_count}),e.jsx("small",{className:"text-muted",children:"Posts"})]}),e.jsxs("div",{className:"col-4",children:[e.jsx("div",{className:"h5 mb-0",children:s.events_count}),e.jsx("small",{className:"text-muted",children:"Events"})]})]})]})]}),e.jsxs("div",{className:"card shadow-sm",children:[e.jsx("div",{className:"card-header",children:e.jsxs("ul",{className:"nav nav-tabs card-header-tabs",children:[e.jsx("li",{className:"nav-item",children:e.jsxs("button",{className:`nav-link ${i==="posts"?"active":""}`,onClick:()=>x("posts"),children:["Posts (",s.posts_count,")"]})}),e.jsx("li",{className:"nav-item",children:e.jsxs("button",{className:`nav-link ${i==="events"?"active":""}`,onClick:()=>x("events"),children:["Events (",s.events_count,")"]})}),e.jsx("li",{className:"nav-item",children:e.jsxs("button",{className:`nav-link ${i==="members"?"active":""}`,onClick:()=>x("members"),children:["Members (",s.members_count,")"]})}),e.jsx("li",{className:"nav-item",children:e.jsx("button",{className:`nav-link ${i==="about"?"active":""}`,onClick:()=>x("about"),children:"About"})})]})}),e.jsxs("div",{className:"card-body",children:[i==="posts"&&e.jsx("div",{children:$?e.jsx("div",{className:"text-center py-4",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})}):v.length>0?e.jsx("div",{className:"space-y-4",children:v.map(t=>e.jsx(R,{post:t,onReact:L,onComment:P,onShare:M},t.id))}):e.jsxs("div",{className:"text-center py-5",children:[e.jsx(W,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h5",{className:"mb-2",children:"No posts yet"}),e.jsx("p",{className:"text-muted",children:"This organization hasn't posted anything yet."})]})}),i==="events"&&e.jsxs("div",{className:"text-center py-5",children:[e.jsx(z,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h5",{className:"mb-2",children:"Events"}),e.jsx("p",{className:"text-muted",children:"Organization events will be shown here."})]}),i==="members"&&e.jsxs("div",{className:"text-center py-5",children:[e.jsx(B,{className:"w-12 h-12 text-muted mx-auto mb-3"}),e.jsx("h5",{className:"mb-2",children:"Members"}),e.jsx("p",{className:"text-muted",children:"Organization members will be shown here."})]}),i==="about"&&e.jsxs("div",{children:[e.jsxs("h5",{className:"mb-3",children:["About ",s.name]}),e.jsx("p",{className:"text-muted mb-4",children:s.description}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6",children:[e.jsx("h6",{children:"Organization Details"}),e.jsxs("ul",{className:"list-unstyled",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Type:"})," ",s.type]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Campus:"})," ",s.campus]}),s.established_date&&e.jsxs("li",{children:[e.jsx("strong",{children:"Established:"})," ",new Date(s.established_date).toLocaleDateString()]}),s.location&&e.jsxs("li",{children:[e.jsx("strong",{children:"Location:"})," ",s.location]})]})]}),e.jsxs("div",{className:"col-md-6",children:[e.jsx("h6",{children:"Contact Information"}),e.jsxs("ul",{className:"list-unstyled",children:[s.email&&e.jsxs("li",{children:[e.jsx("strong",{children:"Email:"})," ",e.jsx("a",{href:`mailto:${s.email}`,children:s.email})]}),s.phone&&e.jsxs("li",{children:[e.jsx("strong",{children:"Phone:"})," ",s.phone]}),s.website&&e.jsxs("li",{children:[e.jsx("strong",{children:"Website:"})," ",e.jsx("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",children:s.website})]})]})]})]})]})]})]})]})})})]})}export{me as default};
