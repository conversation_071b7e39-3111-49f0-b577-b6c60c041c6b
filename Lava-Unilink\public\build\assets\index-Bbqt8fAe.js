import{a as t}from"./app-logo-icon-BTP1W0LK.js";import{r as o}from"./app-BYRFhzWn.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],n=t("Check",c);function i(r){const e=o.useRef({value:r,previous:r});return o.useMemo(()=>(e.current.value!==r&&(e.current.previous=e.current.value,e.current.value=r),e.current.previous),[r])}export{n as C,i as u};
