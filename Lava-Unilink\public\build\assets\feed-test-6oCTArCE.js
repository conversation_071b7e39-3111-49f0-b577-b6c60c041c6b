import{j as e,L as m}from"./app-FHRWCuO9.js";import{A as t}from"./app-layout-CQXuNuUM.js";import"./app-logo-icon-CDT9SgfN.js";import"./index-CZG6ZiXE.js";import"./index-BJKo6RB-.js";function o({initialPosts:r,feedType:d="personalized"}){var a;return console.log("FeedTest component rendered with:",{initialPosts:r,feedType:d}),e.jsxs(t,{children:[e.jsx(m,{title:"Feed Test"}),e.jsxs("div",{className:"container mx-auto p-4",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Feed Test Page"}),e.jsxs("p",{children:["Feed Type: ",d]}),e.jsxs("p",{children:["Posts Count: ",((a=r==null?void 0:r.data)==null?void 0:a.length)||0]}),r!=null&&r.data&&r.data.length>0?e.jsxs("div",{className:"mt-4",children:[e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Posts:"}),r.data.map(s=>e.jsxs("div",{className:"border p-4 mb-4 rounded",children:[e.jsx("h3",{className:"font-bold",children:s.title}),e.jsxs("p",{className:"text-gray-600",children:[s.content.substring(0,100),"..."]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["By: ",s.user.name]})]},s.id))]}):e.jsx("p",{className:"mt-4 text-gray-500",children:"No posts available"})]})]})}export{o as default};
